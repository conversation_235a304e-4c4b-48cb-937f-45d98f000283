{"chatRequest": {"basic": {"messages": [{"role": "user", "content": "你好，请介绍一下你自己", "id": "msg_001", "timestamp": 1703123456789}], "model": "gpt-4", "stream": false, "conversation_id": "conv_12345", "max_tokens": 2048, "temperature": 0.7}, "withOptions": {"messages": [{"role": "system", "content": "你是一个专业的AI助手，请用友好和专业的语气回答问题。", "id": "msg_system_001", "timestamp": 1703123456789}, {"role": "user", "content": "请帮我分析一下当前的市场趋势，并提供一些投资建议。", "id": "msg_user_001", "timestamp": 1703123456790}], "model": "claude-3-opus", "stream": true, "conversation_id": "conv_67890", "max_tokens": 4096, "temperature": 0.8, "options": {"deep_thinking": true, "online_search": true}}, "multiTurn": {"messages": [{"role": "user", "content": "什么是机器学习？", "id": "msg_001", "timestamp": 1703123456789}, {"role": "assistant", "content": "机器学习是人工智能的一个分支，它使计算机能够在没有明确编程的情况下学习和改进。", "id": "msg_002", "timestamp": 1703123456790}, {"role": "user", "content": "能给我举个具体的例子吗？", "id": "msg_003", "timestamp": 1703123456791}], "model": "gpt-3.5-turbo", "stream": false, "max_tokens": 1024, "temperature": 0.6}}, "textGenerationRequest": {"creative": {"prompt": "写一篇关于未来城市的科幻短篇小说，要求包含人工智能、环保科技和人文关怀等元素。", "model": "claude-3-sonnet", "stream": false, "max_tokens": 3000, "temperature": 0.9, "task_type": "creative", "options": {"style": "科幻文学", "format": "短篇小说", "language": "中文", "deep_thinking": true, "online_search": false}}, "code": {"prompt": "请用Python编写一个简单的Web爬虫，能够抓取指定网站的标题和链接，并保存到CSV文件中。要求包含错误处理和日志记录。", "model": "gpt-4-turbo", "stream": true, "max_tokens": 2048, "temperature": 0.3, "task_type": "code", "options": {"style": "专业代码", "format": "完整程序", "language": "Python", "deep_thinking": false, "online_search": false}}, "document": {"prompt": "为一个在线教育平台编写产品需求文档，包括功能模块、用户角色、技术架构和项目时间线。", "model": "claude-3-opus", "stream": false, "max_tokens": 4096, "temperature": 0.5, "task_type": "document", "options": {"style": "正式商务", "format": "结构化文档", "language": "中文", "deep_thinking": true, "online_search": true}}, "summary": {"prompt": "请总结以下文章的主要内容：[这里是一篇关于人工智能发展趋势的长篇文章内容...]", "model": "gpt-3.5-turbo", "stream": false, "max_tokens": 1024, "temperature": 0.2, "task_type": "summary", "options": {"style": "简洁明了", "format": "要点总结", "language": "中文", "deep_thinking": false, "online_search": false}}, "translation": {"prompt": "请将以下英文文本翻译成中文：'Artificial intelligence is revolutionizing the way we work, learn, and interact with technology. From healthcare to finance, AI applications are becoming increasingly sophisticated and widespread.'", "model": "gpt-4", "stream": false, "max_tokens": 512, "temperature": 0.1, "task_type": "translation", "options": {"style": "准确流畅", "format": "直译", "language": "中文", "deep_thinking": false, "online_search": false}}, "rewrite": {"prompt": "请将以下文本改写得更加正式和专业：'这个项目挺不错的，我们应该考虑一下，可能会有很好的效果。'", "model": "claude-3-sonnet", "stream": false, "max_tokens": 256, "temperature": 0.4, "task_type": "rewrite", "options": {"style": "正式专业", "format": "商务语言", "language": "中文", "deep_thinking": false, "online_search": false}}, "qa": {"prompt": "什么是区块链技术？它有哪些主要应用场景？", "model": "gpt-4-turbo", "stream": false, "max_tokens": 1536, "temperature": 0.3, "task_type": "qa", "options": {"style": "教育性", "format": "问答形式", "language": "中文", "deep_thinking": true, "online_search": true}}, "general": {"prompt": "请为我制定一个为期一个月的健身计划，包括有氧运动、力量训练和饮食建议。", "model": "claude-3-haiku", "stream": false, "max_tokens": 2048, "temperature": 0.6, "task_type": "general", "options": {"style": "实用指导", "format": "计划表", "language": "中文", "deep_thinking": false, "online_search": false}}}}