# 当贝AI Provider HTTP API 测试工具

## 📖 概述

当贝AI Provider HTTP API 测试工具是一个功能完整的Web界面，专门用于测试当贝AI Provider项目中的所有HTTP API接口。该工具提供了直观的用户界面，支持各种HTTP方法、请求参数配置、响应查看和历史记录管理。

## ✨ 主要功能

### 🔧 核心功能
- **完整的HTTP方法支持**：GET、POST、PUT、DELETE、PATCH、HEAD、OPTIONS
- **智能请求构建**：支持请求头、URL参数、请求体配置
- **多种认证方式**：Bearer Token、Basic Auth、API Key
- **响应格式化**：JSON语法高亮、自动格式化
- **流式响应支持**：Server-Sent Events (SSE) 实时显示

### 🎨 用户体验
- **响应式设计**：支持桌面和移动设备
- **深色/浅色主题**：可切换的主题模式
- **键盘快捷键**：Ctrl+Enter发送请求，Ctrl+K聚焦搜索
- **实时搜索**：快速过滤API接口列表

### 📊 数据管理
- **请求历史记录**：自动保存最近50次请求
- **配置导入导出**：支持JSON格式的配置备份
- **API模板系统**：预设常用接口的测试模板
- **本地存储**：自动保存用户设置和历史记录

## 🚀 快速开始

### 1. 启动服务器

```bash
# 开发模式启动
npm run server

# 或者指定端口
npm run server -- --port 8080

# 生产模式启动
npm run server:prod
```

### 2. 访问测试工具

服务器启动后，在浏览器中访问：

```
http://localhost:3000/api-tester
```

或者直接访问根路径（会自动重定向）：

```
http://localhost:3000/
```

### 3. 开始测试

1. **选择API接口**：从左侧边栏选择要测试的API
2. **配置请求参数**：设置请求头、URL参数、请求体等
3. **发送请求**：点击"发送请求"按钮或使用Ctrl+Enter快捷键
4. **查看响应**：在右侧查看响应状态、响应体和响应头

## 📋 界面说明

### 左侧边栏

#### API接口列表
- **分组显示**：按功能模块分组（模型管理、聊天对话、文本生成、系统接口）
- **搜索功能**：支持按方法和路径搜索接口
- **快速选择**：点击接口自动填充请求信息

#### 请求历史
- **最近记录**：显示最近10次请求
- **状态指示**：成功请求显示绿色边框，失败请求显示红色边框
- **快速加载**：点击历史记录快速恢复请求配置

### 主要内容区域

#### 请求构建器
- **基本信息**：HTTP方法、请求URL
- **请求头**：支持添加/删除自定义请求头
- **URL参数**：支持添加/删除查询参数
- **请求体**：支持JSON、表单、纯文本、XML格式
- **认证配置**：支持多种认证方式

#### 响应显示器
- **响应状态**：状态码、响应时间、响应大小
- **响应体**：JSON语法高亮、格式化显示
- **响应头**：完整的响应头信息
- **流式响应**：实时显示Server-Sent Events数据

## 🔧 高级功能

### 流式请求测试

对于支持流式响应的API（如聊天对话），可以使用流式响应功能：

1. 切换到"流式响应"标签页
2. 点击"开始流式请求"按钮
3. 实时查看流式数据
4. 使用"停止"按钮中断连接

### 配置管理

#### 导出配置
```json
{
  "version": "1.0.0",
  "timestamp": 1640995200000,
  "request": {
    "method": "POST",
    "url": "http://localhost:3000/api/chat",
    "headers": { "Content-Type": "application/json" },
    "body": "{ \"messages\": [...] }"
  },
  "history": [...],
  "theme": "dark"
}
```

#### 导入配置
- 点击"导入配置"按钮
- 选择之前导出的JSON配置文件
- 自动恢复请求设置和历史记录

### API模板

系统预设了常用API的测试模板，包括：

- **模型管理**：获取模型列表、模型信息
- **聊天对话**：普通聊天、流式聊天、带上下文对话
- **文本生成**：代码生成、创意写作、文档摘要、翻译
- **OpenAI兼容**：标准OpenAI格式的请求

## 🎯 使用场景

### 开发调试
- **API开发**：快速测试新开发的API接口
- **参数验证**：验证不同参数组合的响应
- **错误处理**：测试异常情况和错误响应

### 功能验证
- **流式响应**：测试聊天和文本生成的流式输出
- **模型切换**：验证不同AI模型的响应差异
- **性能测试**：监控响应时间和数据大小

### 集成测试
- **OpenAI兼容性**：验证与OpenAI API的兼容性
- **第三方集成**：为第三方开发者提供测试工具
- **文档验证**：确保API文档与实际行为一致

## ⚙️ 配置选项

### 服务器配置

在启动服务器时可以使用以下选项：

```bash
# 指定端口
npm run server -- --port 8080

# 指定主机
npm run server -- --host 0.0.0.0

# 启用调试模式
npm run server -- --debug

# 组合使用
npm run server -- --host 0.0.0.0 --port 8080 --debug
```

### 环境变量

```bash
# 设置端口
export PORT=8080

# 设置主机
export HOST=0.0.0.0

# 启用调试
export DEBUG=true

# 设置CORS源
export CORS_ORIGIN=http://localhost:3000
```

## 🔒 安全注意事项

### 生产环境部署

1. **HTTPS配置**：在生产环境中使用HTTPS
2. **访问控制**：限制测试工具的访问权限
3. **CORS设置**：正确配置跨域资源共享
4. **敏感信息**：避免在测试中使用真实的API密钥

### 数据保护

- **本地存储**：历史记录仅保存在浏览器本地
- **敏感数据**：不要在请求体中包含敏感信息
- **清理功能**：定期清理历史记录和缓存

## 🐛 故障排除

### 常见问题

#### 1. 无法访问测试工具
- 确认服务器已正确启动
- 检查端口是否被占用
- 验证防火墙设置

#### 2. API请求失败
- 检查请求URL是否正确
- 验证请求头和参数格式
- 查看浏览器开发者工具的网络面板

#### 3. 流式响应不工作
- 确认API支持Server-Sent Events
- 检查请求参数中是否包含`stream=true`
- 验证浏览器是否支持EventSource

#### 4. 配置导入失败
- 检查JSON文件格式是否正确
- 确认文件版本兼容性
- 查看浏览器控制台错误信息

### 调试技巧

1. **开启调试模式**：使用`--debug`参数启动服务器
2. **查看控制台**：打开浏览器开发者工具查看详细日志
3. **网络监控**：使用网络面板监控HTTP请求
4. **本地存储**：检查浏览器本地存储中的数据

## 📞 技术支持

如果遇到问题或需要帮助，请：

1. 查看项目文档和API说明
2. 检查GitHub Issues中的已知问题
3. 提交新的Issue并提供详细信息
4. 联系开发团队获取技术支持

## 🔄 更新日志

### v1.0.0 (2024-01-01)
- ✨ 初始版本发布
- 🎨 完整的Web界面
- 🔧 支持所有HTTP方法
- 📊 请求历史记录
- 🌙 深色主题支持
- 🚀 流式响应功能
- 📱 响应式设计

---

**注意**：本工具仅用于开发和测试目的，请勿在生产环境中暴露给未授权用户。
