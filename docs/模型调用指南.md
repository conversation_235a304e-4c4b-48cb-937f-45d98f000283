# 当贝AI模型调用指南

## 概述

本指南基于 `models.json` 文件中列举的模型列表，详细说明如何使用当贝AI Provider SDK调用不同的AI模型。当贝AI平台支持多种先进的AI模型，包括推理模型和高效模型两大类别。

## 支持的AI模型

### 🧠 推理模型（支持深度思考）

这类模型专注于逻辑推理、深度分析和复杂问题解决，适合需要深度思考的场景。

| 模型名称 | 模型值 | 特点 | 推荐度 |
|---------|--------|------|--------|
| **DeepSeek-R1最新版** | `deepseek` | 专注逻辑推理与深度分析，擅长解决复杂问题 | ⭐⭐⭐⭐⭐ |
| **豆包-1.6** | `doubao-1_6-thinking` | 创作、推理、数学大幅增强 | ⭐⭐⭐⭐⭐ |
| **GLM-4.5** | `glm-4-5` | 智谱最新旗舰模型，支持思考模式切换 | ⭐⭐⭐⭐ |
| **通义3-235B** | `qwen3-235b-a22b` | 国内首个混合推理模型，达到业界SOTA水平 | ⭐⭐⭐⭐ |
| **MiniMax-M1** | `MiniMax-M1` | 全球领先，80K思维链 x 1M输入 | ⭐⭐⭐ |
| **通义QwQ** | `qwq-plus` | 善解难题，精准表达，知识全面 | ⭐⭐⭐ |
| **豆包-1.5** | `doubao-thinking` | 推理模型，专精数理编程，擅长创意写作 | ⭐⭐⭐ |

### ⚡ 高效模型（快速响应）

这类模型注重响应速度和效率，适合需要快速回复的场景。

| 模型名称 | 模型值 | 特点 | 推荐度 |
|---------|--------|------|--------|
| **DeepSeek-V3** | `deepseek-v3` | 轻量高效，响应极快，擅长代码分析 | ⭐⭐⭐⭐ |
| **Kimi K2** | `kimi-k2-0711-preview` | 更强代码能力，擅长通用Agent任务 | ⭐⭐⭐⭐ |
| **GLM-4-Plus** | `glm-4-plus` | 智谱最强高智能旗舰模型 | ⭐⭐⭐ |
| **豆包** | `doubao` | 字节全能AI，创意写作、百科解答 | ⭐⭐⭐ |
| **通义Plus** | `qwen-plus` | 复杂问题速解专家，知识广博 | ⭐⭐⭐ |
| **Kimi** | `moonshot-v1-32k` | 高效问题解析者，多领域知识库 | ⭐⭐⭐ |
| **通义Long** | `qwen-long` | 超长上下文处理场景专用 | ⭐⭐ |
| **文心4.5** | `ernie-4.5-turbo-32k` | 广泛适用于各领域复杂任务 | ⭐⭐ |

## 基础调用示例

### 1. 快速开始

```javascript
const { DangbeiProvider } = require('dangbei-provider');

// 创建Provider实例
const provider = new DangbeiProvider({
  debug: true,
  timeout: 60000  // 推理模型可能需要更长时间
});

// 快速聊天（使用默认模型）
async function quickStart() {
  try {
    const response = await provider.quickChat('你好，请介绍一下你自己');
    console.log('AI回复:', response.content);
  } catch (error) {
    console.error('调用失败:', error.message);
  }
}
```

### 2. 指定模型调用

```javascript
// 使用DeepSeek-R1进行逻辑推理
async function useDeepSeekR1() {
  const conversation = await provider.createConversation();
  
  const response = await provider.chatSync({
    conversationId: conversation.conversationId,
    question: '请分析：为什么1+1=2？请从数学逻辑角度详细解释',
    model: 'deepseek'  // 指定使用DeepSeek-R1模型
  });
  
  console.log('DeepSeek-R1回复:', response);
}
```

### 3. 代码分析专用模型

```javascript
// 使用DeepSeek-V3进行代码分析
async function analyzeCode() {
  const conversation = await provider.createConversation();
  
  const response = await provider.chatSync({
    conversationId: conversation.conversationId,
    question: `请分析这段代码的功能并优化：
    \`\`\`python
    def fibonacci(n):
        if n <= 1:
            return n
        return fibonacci(n-1) + fibonacci(n-2)
    \`\`\``,
    model: 'deepseek-v3'  // 使用代码专家模型
  });
  
  console.log('代码分析结果:', response);
}
```

## 流式响应调用

对于需要实时显示AI回复的场景，可以使用流式响应：

```javascript
// 流式响应示例
async function streamingChat() {
  const conversation = await provider.createConversation();
  
  await provider.chat({
    conversationId: conversation.conversationId,
    question: '请写一首关于人工智能的现代诗',
    model: 'glm-4-5',  // 使用GLM-4.5进行创意写作
    callbacks: {
      onMessage: (content) => {
        process.stdout.write(content);  // 实时显示内容
      },
      onComplete: () => {
        console.log('\n✅ 创作完成！');
      },
      onError: (error) => {
        console.error('❌ 创作失败:', error.message);
      }
    }
  });
}
```

## 高级配置选项

### 启用知识搜索

某些模型支持知识搜索功能，可以获取更准确和最新的信息：

```javascript
async function advancedConfig() {
  const conversation = await provider.createConversation();
  
  const response = await provider.chatSync({
    conversationId: conversation.conversationId,
    question: '请分析2024年人工智能技术的最新发展趋势',
    model: 'glm-4-5',
    chatOption: {
      searchKnowledge: true,        // 启用知识搜索
      searchAllKnowledge: false,    // 不搜索所有知识库
      searchSharedKnowledge: true   // 启用共享知识库搜索
    }
  });
  
  console.log('带知识搜索的回复:', response);
}
```

## 模型选择建议

### 根据使用场景选择模型

1. **逻辑推理和数学问题**
   - 推荐：`deepseek`（DeepSeek-R1）
   - 备选：`doubao-1_6-thinking`（豆包-1.6）

2. **代码分析和编程**
   - 推荐：`deepseek-v3`（DeepSeek-V3）
   - 备选：`kimi-k2-0711-preview`（Kimi K2）

3. **创意写作和文学创作**
   - 推荐：`glm-4-5`（GLM-4.5）
   - 备选：`doubao-1_6-thinking`（豆包-1.6）

4. **快速问答和日常对话**
   - 推荐：`qwen-plus`（通义Plus）
   - 备选：`doubao`（豆包）

5. **长文本处理**
   - 推荐：`qwen-long`（通义Long）
   - 备选：`MiniMax-M1`

### 性能考虑

- **推理模型**：响应时间较长（10-30秒），但回答质量高
- **高效模型**：响应时间较短（3-10秒），适合快速交互
- **建议超时设置**：推理模型60秒，高效模型30秒

## 错误处理

```javascript
async function robustModelCall() {
  try {
    const response = await provider.chatSync({
      conversationId: 'your-conversation-id',
      question: 'your-question',
      model: 'deepseek'
    });
    
    return response;
    
  } catch (error) {
    console.error('模型调用失败:', error.message);
    
    // 可以尝试使用备用模型
    try {
      console.log('尝试使用备用模型...');
      const fallbackResponse = await provider.chatSync({
        conversationId: 'your-conversation-id',
        question: 'your-question',
        model: 'doubao'  // 使用更稳定的备用模型
      });
      
      return fallbackResponse;
      
    } catch (fallbackError) {
      console.error('备用模型也失败:', fallbackError.message);
      throw fallbackError;
    }
  }
}
```

## 最佳实践

1. **模型选择**：根据具体任务选择合适的模型
2. **超时设置**：为推理模型设置更长的超时时间
3. **错误处理**：实现备用模型机制
4. **流式响应**：对于长回复使用流式响应提升用户体验
5. **请求频率**：避免过于频繁的请求，建议间隔2-3秒
6. **调试模式**：开发时启用debug模式查看详细日志

## 完整示例

查看以下示例文件获取更多详细用法：

- `examples/model-usage-examples.ts` - TypeScript完整示例
- `examples/model-usage-simple.js` - JavaScript简化示例
- `examples/basic-usage.ts` - 基础功能演示
- `examples/advanced-usage.ts` - 高级功能演示

## 注意事项

1. 某些模型可能不支持特定功能（如深度思考模式）
2. 推理模型的响应时间较长，请耐心等待
3. 建议在生产环境中实现重试机制
4. 注意API调用频率限制，避免被限流
