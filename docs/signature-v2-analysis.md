# 当贝AI v2接口签名算法分析与实现

## 概述

本文档详细分析了当贝AI v2接口的签名算法，基于对浏览器端 JavaScript 代码的深度逆向分析，特别是 `_app-72ae859153e99355.js` 文件中的 WASM 签名逻辑。

## 核心发现

### 1. 签名函数结构

在浏览器端 JavaScript 中，签名生成的核心函数是 `v(e, t)`：

```javascript
function v(e, t) {
    var n = g(e, r.__wbindgen_malloc, r.__wbindgen_realloc)  // 写入第一个字符串
      , o = p                                                // 获取第一个字符串长度
      , i = g(t, r.__wbindgen_malloc, r.__wbindgen_realloc)  // 写入第二个字符串
      , a = p;                                               // 获取第二个字符串长度
    return r.get_sign(n, o, i, a)                           // 调用 WASM 签名函数
}
```

### 2. 内存管理函数

`g(e, t, n)` 函数负责将字符串写入 WASM 内存，包含两种处理模式：

```javascript
function g(e, t, n) {
    if (void 0 === n) {
        // 简单模式：直接 UTF-8 编码
        var r = h.encode(e)  // h 是 TextEncoder
          , o = t(r.length, 1) >>> 0;  // 分配内存
        return f().subarray(o, o + r.length).set(r),  // 写入内存
        p = r.length,  // 更新全局长度变量
        o  // 返回指针
    }

    // 优化模式：ASCII 快速路径 + 混合处理
    for (var i = e.length, a = t(i, 1) >>> 0, s = f(), c = 0; c < i; c++) {
        var l = e.charCodeAt(c);
        if (l > 127) break;  // 遇到非 ASCII 字符跳出
        s[a + c] = l;        // 直接写入字符码
    }

    if (c !== i) {
        // 处理剩余的非 ASCII 字符
        0 !== c && (e = e.slice(c));  // 截取剩余部分
        a = n(a, i, i = c + 3 * e.length, 1) >>> 0;  // 重新分配内存
        var u = m(e, f().subarray(a + c, a + i));     // 使用 encodeInto
        c += u.written;                               // 更新写入计数
        a = n(a, i, c, 1) >>> 0;                     // 调整内存大小
    }

    return p = c, a;  // 更新长度并返回指针
}
```

#### 关键特性：

1. **双模式处理**：
   - 简单模式：直接 UTF-8 编码（当 `n === undefined`）
   - 优化模式：ASCII 快速路径 + 混合处理（当 `n` 存在）

2. **ASCII 快速路径**：
   - 对纯 ASCII 字符串避免 UTF-8 编码开销
   - 直接使用 `charCodeAt()` 写入字符码

3. **混合处理**：
   - 保留已处理的 ASCII 部分
   - 对非 ASCII 部分使用 UTF-8 编码
   - 动态调整内存分配大小

### 3. 关键变量

- `p`: 全局变量，记录最后写入字符串的长度
- `r`: WASM 实例对象
- `h`: TextEncoder 实例
- `f()`: 返回 WASM 内存的 Uint8Array 视图

## 实现策略

### TypeScript 实现

我们在 `src/utils/signature-v2.ts` 中实现了完整的签名算法模拟：

#### 1. WASM 实例初始化

```typescript
private static initNativeWasmSync(): void {
    // 同步加载 sign_bg.wasm 文件
    // 创建 WebAssembly.Instance
    // 配置导入对象
}
```

#### 2. 内存管理

```typescript
private static writeStringToMemory(str: string): number {
    // 获取 WASM 函数引用
    const malloc = (this.wasmInstance.exports as any).__wbindgen_malloc;
    const realloc = (this.wasmInstance.exports as any).__wbindgen_realloc;

    // 使用优化模式（对应原始 g 函数传入 realloc 的情况）
    return this.writeStringToMemoryOptimized(str, malloc, realloc);
}

private static writeStringToMemoryOptimized(
    str: string,
    malloc: (size: number, align: number) => number,
    realloc: (ptr: number, oldSize: number, newSize: number, align: number) => number
): number {
    // 第一阶段：ASCII 快速路径
    // 遍历字符串，直接写入 ASCII 字符码

    // 第二阶段：混合处理
    // 对非 ASCII 字符使用 UTF-8 编码
    // 重新分配内存以适应新的长度

    // 更新全局长度变量
    this.lastStringLength = writtenCount;
}
```

#### 3. 签名生成

```typescript
private static getSignatureFromNativeWasm(s1: string, s2: string): string {
    // 严格模拟 v(e, t) 函数行为
    // 1. 写入第一个字符串，获取指针和长度
    // 2. 写入第二个字符串，获取指针和长度
    // 3. 调用 get_sign(ptr1, len1, ptr2, len2)
    // 4. 处理返回值并验证格式
}
```

## 参数说明

### 签名函数参数

根据分析，`v(e, t)` 函数的两个参数含义：

1. **第一个参数 (e)**: 请求数据
   - POST 请求：原始 body 字符串
   - GET 请求：查询字符串部分（不含 `?`）

2. **第二个参数 (t)**: URL 路径
   - 仅包含路径部分，如 `/ai-search/chatApi/v2/chat`
   - 不包含协议、主机、查询参数

### WASM 函数调用

最终的 WASM 函数调用格式：
```
get_sign(ptr1, len1, ptr2, len2)
```

其中：
- `ptr1`: 第一个字符串在 WASM 内存中的指针
- `len1`: 第一个字符串的字节长度
- `ptr2`: 第二个字符串在 WASM 内存中的指针
- `len2`: 第二个字符串的字节长度

## 调试信息

实现中包含详细的调试输出：

```typescript
console.log('🔐 WASM签名参数准备:', {
    s1: { ptr: `0x${ptr1.toString(16)}`, len: len1, preview: s1.slice(0, 50) },
    s2: { ptr: `0x${ptr2.toString(16)}`, len: len2, preview: s2.slice(0, 50) }
});
```

## 使用示例

```typescript
import { SignatureV2Utils } from './utils/signature-v2';

// 生成 v2 接口签名
const signature = SignatureV2Utils.generateV2Signature({
    method: 'POST',
    url: '/ai-search/chatApi/v2/chat',
    bodyRaw: '{"message":"hello"}',
    timestamp: Date.now(),
    nonce: 'random_string'
});

console.log('生成的签名:', signature);
```

## 注意事项

1. **WASM 文件依赖**: 需要确保 `sign_bg.wasm` 文件存在于项目根目录
2. **参数一致性**: 传入的参数必须与实际发送的请求完全一致
3. **编码格式**: 字符串使用 UTF-8 编码写入 WASM 内存
4. **返回格式**: 签名通常是32位十六进制字符串（大写）

## 错误处理

实现包含多层降级策略：
1. 优先使用原生 WASM 调用
2. 降级到 WASM 模拟器
3. 最终降级到传统 MD5 算法

## 性能考虑

- WASM 实例仅初始化一次
- 内存分配使用 WASM 的原生分配器
- 支持 ASCII 快速路径优化（可选）

## 兼容性

- Node.js 环境：完全支持
- 浏览器环境：需要 WebAssembly 支持
- TypeScript：提供完整类型定义
