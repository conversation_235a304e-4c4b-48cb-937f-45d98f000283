#!/usr/bin/env node

/**
 * 简化的三色数据测试脚本
 * 快速验证三色数据监控和处理功能
 */

const { DangbeiProvider } = require('./dist');

// 颜色输出函数
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function colorLog(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

/**
 * 简单的三色数据统计
 */
class SimpleStats {
  constructor() {
    this.stats = {
      progress: 0,
      thinking: 0,
      text: 0,
      card: 0,
      unknown: 0,
      total: 0
    };
    this.messages = [];
  }

  record(contentType, content, messageId) {
    this.stats.total++;
    if (this.stats.hasOwnProperty(contentType)) {
      this.stats[contentType]++;
    } else {
      this.stats.unknown++;
    }

    this.messages.push({
      contentType,
      content: content.substring(0, 100),
      messageId,
      timestamp: new Date().toLocaleTimeString()
    });

    // 实时显示
    const icons = {
      progress: '🔍',
      thinking: '🤔',
      text: '💬',
      card: '📋',
      unknown: '❓'
    };

    const colorMap = {
      progress: 'blue',
      thinking: 'yellow',
      text: 'green',
      card: 'magenta',
      unknown: 'red'
    };

    const icon = icons[contentType] || '❓';
    const color = colorMap[contentType] || 'red';
    
    colorLog(color, `${icon} [${contentType.toUpperCase()}] ${content.substring(0, 80)}${content.length > 80 ? '...' : ''}`);
  }

  report() {
    colorLog('cyan', '\n=== 三色数据测试结果 ===');
    console.log(`总消息数: ${this.stats.total}`);
    
    Object.keys(this.stats).forEach(type => {
      if (type !== 'total' && this.stats[type] > 0) {
        const percentage = ((this.stats[type] / this.stats.total) * 100).toFixed(1);
        console.log(`${type}: ${this.stats[type]} (${percentage}%)`);
      }
    });

    // 简单诊断
    const expectedTypes = ['progress', 'thinking', 'text', 'card'];
    const missingTypes = expectedTypes.filter(type => this.stats[type] === 0);
    
    if (missingTypes.length === 0) {
      colorLog('green', '\n✅ 所有三色数据类型都已接收');
    } else {
      colorLog('yellow', `\n⚠️ 缺失的数据类型: ${missingTypes.join(', ')}`);
    }

    if (this.stats.unknown > 0) {
      colorLog('red', `\n❌ 发现 ${this.stats.unknown} 个未知类型的消息`);
    }
  }
}

/**
 * 快速测试三色数据
 */
async function quickTest() {
  colorLog('cyan', '🚀 开始三色数据快速测试');
  colorLog('cyan', '=' .repeat(40));

  const provider = new DangbeiProvider({
    debug: true
  });

  const stats = new SimpleStats();

  try {
    // 创建对话
    colorLog('blue', '\n📝 创建测试对话...');
    const conversation = await provider.createConversation();
    console.log(`对话ID: ${conversation.conversationId}`);

    // 简单的测试问题
    const question = '请简单介绍一下人工智能';
    
    colorLog('blue', `\n💬 发送问题: ${question}`);
    
    // 设置超时
    const timeout = setTimeout(() => {
      colorLog('yellow', '\n⏰ 测试超时，生成报告...');
      stats.report();
      process.exit(0);
    }, 15000); // 15秒超时

    // 开始聊天
    await provider.chat({
      conversationId: conversation.conversationId,
      question: question,
      callbacks: {
        onMessage: (content, data) => {
          stats.record(data.content_type || 'unknown', content, data.id);
        },
        onComplete: (data) => {
          clearTimeout(timeout);
          colorLog('green', '\n✅ 对话完成');
          console.log(`完成消息ID: ${data.id}`);
          
          // 等待一秒后生成报告
          setTimeout(() => {
            stats.report();
            process.exit(0);
          }, 1000);
        },
        onError: (error) => {
          clearTimeout(timeout);
          colorLog('red', `\n❌ 对话出错: ${error.message}`);
          stats.report();
          process.exit(1);
        }
      }
    });

  } catch (error) {
    colorLog('red', `\n💥 测试失败: ${error.message}`);
    console.error(error);
    process.exit(1);
  }
}

/**
 * 测试监控器功能
 */
async function testMonitoring() {
  colorLog('cyan', '🔍 测试三色数据监控功能');
  
  // 模拟一些测试数据
  const testMessages = [
    { content_type: 'progress', content: '联网搜索中...', id: 'test-1' },
    { content_type: 'card', content: '{"cardType":"DB-CARD-2","cardInfo":{}}', id: 'test-2' },
    { content_type: 'thinking', content: '用户询问关于人工智能的问题', id: 'test-3' },
    { content_type: 'text', content: '人工智能是一门综合性学科', id: 'test-4' }
  ];

  try {
    // 动态导入监控器（如果可用）
    const { threeColorDataMonitor } = require('./dist/utils/three-color-data-monitor');
    
    colorLog('green', '✅ 三色数据监控器加载成功');
    
    // 模拟消息处理
    testMessages.forEach(msg => {
      threeColorDataMonitor.recordMessage(msg, true);
    });
    
    // 生成报告
    console.log(threeColorDataMonitor.getStatsReport());
    
    // 获取诊断信息
    const diagnostics = threeColorDataMonitor.getDiagnostics();
    if (diagnostics.length > 0) {
      colorLog('yellow', '\n⚠️ 诊断信息:');
      diagnostics.forEach(diag => {
        const color = diag.level === 'error' ? 'red' : 'yellow';
        colorLog(color, `${diag.level.toUpperCase()}: ${diag.message}`);
        console.log(`建议: ${diag.suggestion}`);
      });
    } else {
      colorLog('green', '\n✅ 监控器功能正常');
    }
    
  } catch (error) {
    colorLog('red', `❌ 监控器测试失败: ${error.message}`);
  }
}

/**
 * 主函数
 */
async function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--monitor')) {
    await testMonitoring();
  } else {
    await quickTest();
  }
}

// 运行测试
if (require.main === module) {
  main().catch(error => {
    colorLog('red', `\n💥 程序异常: ${error.message}`);
    console.error(error);
    process.exit(1);
  });
}

module.exports = { quickTest, testMonitoring };
