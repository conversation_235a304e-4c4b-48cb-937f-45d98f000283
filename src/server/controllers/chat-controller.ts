/**
 * 聊天控制器
 * 处理聊天对话相关的 HTTP 请求
 */

import { Request, Response } from 'express';
import { DangbeiProvider } from '../../providers';
import { ModelService } from '../services/model-service';
import { 
  ApiResponse, 
  ChatRequest, 
  ChatResponse, 
  ChatStreamChunk,
  ErrorResponse 
} from '../types/api';
import { ChatCallbacks, SSEMessageDelta, SSEChatCompleted } from '../../types';

/**
 * 聊天控制器类
 * 提供聊天对话相关的 API 端点处理
 */
export class ChatController {
  private readonly dangbeiProvider: DangbeiProvider;
  private readonly modelService: ModelService;

  constructor() {
    // 初始化当贝 Provider，启用调试模式
    this.dangbeiProvider = new DangbeiProvider({
      debug: true
    });
    
    this.modelService = new ModelService();
  }

  /**
   * 处理聊天请求
   * POST /api/chat
   * 
   * @param req Express 请求对象
   * @param res Express 响应对象
   */
  public chat = async (req: Request, res: Response): Promise<void> => {
    const requestId = this.generateRequestId();
    
    try {
      console.log(`[${requestId}] 处理聊天请求`);
      
      // 验证请求体
      const validationResult = this.validateChatRequest(req.body);
      if (!validationResult.valid) {
        const errorResponse: ApiResponse<ErrorResponse> = {
          success: false,
          error: {
            code: 'INVALID_REQUEST',
            message: '请求参数无效',
            details: validationResult.errors
          },
          requestId,
          timestamp: Date.now()
        };
        
        res.status(400).json(errorResponse);
        return;
      }

      const chatRequest: ChatRequest = req.body;
      
      // 验证模型是否支持
      if (!this.modelService.isModelSupported(chatRequest.model)) {
        const errorResponse: ApiResponse<ErrorResponse> = {
          success: false,
          error: {
            code: 'UNSUPPORTED_MODEL',
            message: `不支持的模型: ${chatRequest.model}`
          },
          requestId,
          timestamp: Date.now()
        };
        
        res.status(400).json(errorResponse);
        return;
      }

      // 处理流式响应
      if (chatRequest.stream) {
        await this.handleStreamChat(chatRequest, res, requestId);
      } else {
        await this.handleNormalChat(chatRequest, res, requestId);
      }

    } catch (error) {
      console.error(`[${requestId}] 聊天请求处理失败:`, error);
      
      if (!res.headersSent) {
        const errorResponse: ApiResponse<ErrorResponse> = {
          success: false,
          error: {
            code: 'CHAT_ERROR',
            message: '聊天请求处理失败',
            details: error instanceof Error ? error.message : '未知错误'
          },
          requestId,
          timestamp: Date.now()
        };

        res.status(500).json(errorResponse);
      }
    }
  };

  /**
   * 处理普通聊天请求（非流式）
   * 
   * @param chatRequest 聊天请求
   * @param res Express 响应对象
   * @param requestId 请求ID
   * @private
   */
  private async handleNormalChat(
    chatRequest: ChatRequest, 
    res: Response, 
    requestId: string
  ): Promise<void> {
    console.log(`[${requestId}] 处理非流式聊天请求`);
    
    // 获取或创建对话ID
    let conversationId = chatRequest.conversation_id;
    if (!conversationId) {
      console.log(`[${requestId}] 创建新对话`);
      const conversation = await this.dangbeiProvider.createConversation();
      conversationId = conversation.conversationId;
    }

    // 获取用户消息（取最后一条用户消息）
    const userMessages = chatRequest.messages.filter(msg => msg.role === 'user');
    if (userMessages.length === 0) {
      throw new Error('没有找到用户消息');
    }

    const lastUserMessage = userMessages[userMessages.length - 1];
    if (!lastUserMessage) {
      throw new Error('没有找到有效的用户消息');
    }

    // 构建聊天选项
    const chatOptions = {
      conversationId,
      question: lastUserMessage.content,
      model: chatRequest.model,
      chatOption: {
        searchKnowledge: false,
        searchAllKnowledge: false,
        searchSharedKnowledge: false
      },
      // 传递options参数用于转换为userAction
      options: chatRequest.options
    };

    // 发送聊天请求
    const response = await this.dangbeiProvider.chat(chatOptions);
    
    // 构建响应
    const chatResponse: ChatResponse = {
      message: {
        role: 'assistant',
        content: response.content,
        id: response.messageId,
        timestamp: Date.now()
      },
      conversation_id: response.conversationId,
      message_id: response.messageId,
      parent_message_id: response.parentMessageId,
      request_id: response.requestId,
      model: chatRequest.model,
      finish_reason: 'stop'
    };

    const apiResponse: ApiResponse<ChatResponse> = {
      success: true,
      data: chatResponse,
      requestId,
      timestamp: Date.now()
    };

    console.log(`[${requestId}] 非流式聊天请求处理完成`);
    
    res.set({
      'Content-Type': 'application/json',
      'X-Request-ID': requestId
    });
    
    res.status(200).json(apiResponse);
  }

  /**
   * 处理流式聊天请求
   * 
   * @param chatRequest 聊天请求
   * @param res Express 响应对象
   * @param requestId 请求ID
   * @private
   */
  private async handleStreamChat(
    chatRequest: ChatRequest, 
    res: Response, 
    requestId: string
  ): Promise<void> {
    console.log(`[${requestId}] 处理流式聊天请求`);
    
    // 设置 SSE 响应头
    res.set({
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'X-Request-ID': requestId
    });

    // 获取或创建对话ID
    let conversationId = chatRequest.conversation_id;
    if (!conversationId) {
      console.log(`[${requestId}] 创建新对话`);
      const conversation = await this.dangbeiProvider.createConversation();
      conversationId = conversation.conversationId;
    }

    // 获取用户消息
    const userMessages = chatRequest.messages.filter(msg => msg.role === 'user');
    if (userMessages.length === 0) {
      throw new Error('没有找到用户消息');
    }

    const lastUserMessage = userMessages[userMessages.length - 1];
    if (!lastUserMessage) {
      throw new Error('没有找到有效的用户消息');
    }

    // 构建聊天选项和回调
    const callbacks: ChatCallbacks = {
      onMessage: (content: string, data: SSEMessageDelta) => {
        const chunk: ChatStreamChunk = {
          id: data.id,
          object: 'chat.completion.chunk',
          created: Math.floor(data.created_at / 1000),
          model: chatRequest.model,
          choices: [{
            index: 0,
            delta: {
              content: content
            },
            finish_reason: null
          }]
        };
        
        res.write(`data: ${JSON.stringify(chunk)}\n\n`);
      },
      
      onComplete: (data: SSEChatCompleted) => {
        const finalChunk: ChatStreamChunk = {
          id: data.id,
          object: 'chat.completion.chunk',
          created: Math.floor(Date.now() / 1000),
          model: chatRequest.model,
          choices: [{
            index: 0,
            delta: {},
            finish_reason: 'stop'
          }]
        };
        
        res.write(`data: ${JSON.stringify(finalChunk)}\n\n`);
        res.write('data: [DONE]\n\n');
        res.end();
        
        console.log(`[${requestId}] 流式聊天请求处理完成`);
      },
      
      onError: (error: Error) => {
        console.error(`[${requestId}] 流式聊天出错:`, error);
        
        const errorChunk = {
          error: {
            code: 'STREAM_ERROR',
            message: error.message
          }
        };
        
        res.write(`data: ${JSON.stringify(errorChunk)}\n\n`);
        res.end();
      }
    };

    const chatOptions = {
      conversationId,
      question: lastUserMessage.content,
      model: chatRequest.model,
      chatOption: {
        searchKnowledge: false,
        searchAllKnowledge: false,
        searchSharedKnowledge: false
      },
      // 传递options参数用于转换为userAction
      options: chatRequest.options,
      callbacks
    };

    // 发送流式聊天请求
    await this.dangbeiProvider.chat(chatOptions, callbacks);
  }

  /**
   * 验证聊天请求参数
   * 
   * @param body 请求体
   * @returns 验证结果
   * @private
   */
  private validateChatRequest(body: any): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!body) {
      errors.push('请求体不能为空');
      return { valid: false, errors };
    }

    if (!body.messages || !Array.isArray(body.messages)) {
      errors.push('messages 字段必须是数组');
    } else if (body.messages.length === 0) {
      errors.push('messages 数组不能为空');
    } else {
      // 验证消息格式
      body.messages.forEach((msg: any, index: number) => {
        if (!msg.role || !['user', 'assistant', 'system'].includes(msg.role)) {
          errors.push(`消息 ${index} 的 role 字段无效`);
        }
        if (!msg.content || typeof msg.content !== 'string') {
          errors.push(`消息 ${index} 的 content 字段无效`);
        }
      });
    }

    if (!body.model || typeof body.model !== 'string') {
      errors.push('model 字段必须是字符串');
    }

    if (body.stream !== undefined && typeof body.stream !== 'boolean') {
      errors.push('stream 字段必须是布尔值');
    }

    return { valid: errors.length === 0, errors };
  }

  /**
   * 生成请求ID
   * 
   * @returns 唯一的请求ID
   * @private
   */
  private generateRequestId(): string {
    return `chat_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
