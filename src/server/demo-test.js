#!/usr/bin/env node

/**
 * 当贝 Provider API 测试工具演示脚本
 * 展示如何使用各种测试工具和功能
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function colorLog(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function printHeader(title) {
  console.log('\n' + '='.repeat(60));
  colorLog('cyan', `🎯 ${title}`);
  console.log('='.repeat(60));
}

function printStep(step, description) {
  colorLog('blue', `\n📋 步骤 ${step}: ${description}`);
}

// 检查文件是否存在
function checkFile(filePath, description) {
  const fullPath = path.resolve(filePath);
  const exists = fs.existsSync(fullPath);
  
  if (exists) {
    colorLog('green', `✅ ${description}: ${filePath}`);
  } else {
    colorLog('red', `❌ ${description}: ${filePath} (文件不存在)`);
  }
  
  return exists;
}

// 执行命令
function runCommand(command, args = [], options = {}) {
  return new Promise((resolve, reject) => {
    colorLog('yellow', `🚀 执行命令: ${command} ${args.join(' ')}`);
    
    const child = spawn(command, args, {
      stdio: 'inherit',
      shell: true,
      ...options
    });
    
    child.on('close', (code) => {
      if (code === 0) {
        colorLog('green', `✅ 命令执行成功`);
        resolve(code);
      } else {
        colorLog('red', `❌ 命令执行失败，退出码: ${code}`);
        reject(new Error(`命令失败，退出码: ${code}`));
      }
    });
    
    child.on('error', (error) => {
      colorLog('red', `❌ 命令执行错误: ${error.message}`);
      reject(error);
    });
  });
}

// 等待用户输入
function waitForInput(message) {
  return new Promise((resolve) => {
    const readline = require('readline');
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });
    
    rl.question(colorLog('yellow', message + ' (按回车继续)'), () => {
      rl.close();
      resolve();
    });
  });
}

// 主演示流程
async function runDemo() {
  try {
    printHeader('当贝 Provider API 测试工具演示');
    
    colorLog('magenta', '🎉 欢迎使用当贝 Provider API 测试工具演示！');
    colorLog('magenta', '本演示将展示如何使用各种测试工具来验证 API 功能。');
    
    await waitForInput('\n按回车开始演示...');
    
    // 步骤1: 检查测试文件
    printStep(1, '检查测试工具文件');
    
    const files = [
      ['src/server/api-test-page.html', '交互式测试页面'],
      ['src/server/api-test-script.js', '自动化测试脚本'],
      ['src/server/test-api-params.json', '请求参数示例'],
      ['src/server/types/api.ts', 'TypeScript 类型定义'],
      ['src/server/API-测试文档.md', 'API 测试文档'],
      ['src/server/README-测试工具.md', '测试工具使用指南']
    ];
    
    let allFilesExist = true;
    files.forEach(([filePath, description]) => {
      if (!checkFile(filePath, description)) {
        allFilesExist = false;
      }
    });
    
    if (!allFilesExist) {
      colorLog('red', '\n❌ 部分测试文件缺失，请先生成完整的测试工具套件。');
      process.exit(1);
    }
    
    await waitForInput('\n文件检查完成，按回车继续...');
    
    // 步骤2: 展示测试参数示例
    printStep(2, '展示 API 请求参数示例');
    
    try {
      const paramsContent = fs.readFileSync('src/server/test-api-params.json', 'utf8');
      const params = JSON.parse(paramsContent);
      
      colorLog('green', '📄 测试参数文件内容预览:');
      console.log('- 包含的接口:', Object.keys(params.endpoints).join(', '));
      console.log('- 错误示例数量:', Object.keys(params.errorExamples).length);
      console.log('- 文件大小:', (paramsContent.length / 1024).toFixed(2) + ' KB');
      
    } catch (error) {
      colorLog('red', `❌ 读取参数文件失败: ${error.message}`);
    }
    
    await waitForInput('\n参数示例展示完成，按回车继续...');
    
    // 步骤3: 检查 package.json 脚本
    printStep(3, '检查 npm 脚本配置');
    
    try {
      const packageContent = fs.readFileSync('package.json', 'utf8');
      const packageJson = JSON.parse(packageContent);
      
      const testScripts = Object.keys(packageJson.scripts).filter(key => key.includes('test:api'));
      
      if (testScripts.length > 0) {
        colorLog('green', '✅ 发现以下 API 测试脚本:');
        testScripts.forEach(script => {
          console.log(`   - npm run ${script}`);
        });
      } else {
        colorLog('yellow', '⚠️  未发现 API 测试脚本，建议添加到 package.json');
      }
      
    } catch (error) {
      colorLog('red', `❌ 读取 package.json 失败: ${error.message}`);
    }
    
    await waitForInput('\n脚本检查完成，按回车继续...');
    
    // 步骤4: 演示自动化测试（如果用户同意）
    printStep(4, '演示自动化测试功能');
    
    colorLog('yellow', '⚠️  注意: 自动化测试需要服务器运行在 http://localhost:3000');
    colorLog('yellow', '如果服务器未运行，测试将会失败。');
    
    const readline = require('readline');
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });
    
    const runTest = await new Promise((resolve) => {
      rl.question('是否运行自动化测试？(y/N): ', (answer) => {
        rl.close();
        resolve(answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes');
      });
    });
    
    if (runTest) {
      try {
        colorLog('blue', '🧪 开始运行自动化测试...');
        await runCommand('node', ['src/server/api-test-script.js', '--verbose']);
        colorLog('green', '🎉 自动化测试演示完成！');
      } catch (error) {
        colorLog('red', '❌ 自动化测试失败，这是正常的（如果服务器未运行）');
        colorLog('yellow', '💡 提示: 先运行 "npm run server:dev" 启动服务器，然后再运行测试');
      }
    } else {
      colorLog('blue', '⏭️  跳过自动化测试演示');
    }
    
    // 步骤5: 展示使用建议
    printStep(5, '使用建议和下一步');
    
    colorLog('cyan', '\n🎯 推荐的使用流程:');
    console.log('1. 启动开发服务器: npm run server:dev');
    console.log('2. 运行自动化测试: npm run test:api:verbose');
    console.log('3. 使用浏览器打开: src/server/api-test-page.html');
    console.log('4. 查看详细文档: src/server/API-测试文档.md');
    
    colorLog('green', '\n📚 学习资源:');
    console.log('- 测试工具使用指南: src/server/README-测试工具.md');
    console.log('- TypeScript 类型定义: src/server/types/api.ts');
    console.log('- 请求参数示例: src/server/test-api-params.json');
    
    colorLog('magenta', '\n🔧 自定义配置:');
    console.log('- 设置 API 地址: API_BASE_URL=https://your-api.com npm run test:api');
    console.log('- 设置 API 密钥: API_KEY=your-key npm run test:api');
    console.log('- 详细输出模式: npm run test:api:verbose');
    
    printHeader('演示完成');
    colorLog('green', '🎉 恭喜！您已经了解了当贝 Provider API 测试工具的基本使用方法。');
    colorLog('blue', '💡 建议您现在开始实际测试您的 API 接口。');
    colorLog('yellow', '📖 如有疑问，请查看相关文档或联系开发团队。');
    
  } catch (error) {
    colorLog('red', `❌ 演示过程中发生错误: ${error.message}`);
    process.exit(1);
  }
}

// 显示帮助信息
function showHelp() {
  console.log(`
当贝 Provider API 测试工具演示

用法:
  node demo-test.js [选项]

选项:
  -h, --help        显示帮助信息

功能:
  - 检查测试工具文件完整性
  - 展示 API 请求参数示例
  - 演示自动化测试功能
  - 提供使用建议和最佳实践

示例:
  node demo-test.js
`);
}

// 主程序入口
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  showHelp();
  process.exit(0);
}

// 捕获异常
process.on('unhandledRejection', (reason, promise) => {
  colorLog('red', `❌ 未处理的Promise拒绝: ${reason}`);
  process.exit(1);
});

process.on('uncaughtException', (error) => {
  colorLog('red', `❌ 未捕获的异常: ${error.message}`);
  process.exit(1);
});

// 启动演示
runDemo().catch(error => {
  colorLog('red', `❌ 演示执行失败: ${error.message}`);
  process.exit(1);
});
