#!/usr/bin/env node

/**
 * 当贝 Provider API 自动化测试脚本
 * 用于批量测试 HTTP API 接口的功能和性能
 */

const https = require('https');
const http = require('http');
const { URL } = require('url');

// 测试配置
const CONFIG = {
  baseUrl: process.env.API_BASE_URL || 'http://localhost:3000',
  apiKey: process.env.API_KEY || '',
  timeout: 30000, // 30秒超时
  retries: 3, // 重试次数
  verbose: process.argv.includes('--verbose') || process.argv.includes('-v')
};

// 颜色输出工具
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function colorLog(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// HTTP 请求工具
function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const isHttps = urlObj.protocol === 'https:';
    const client = isHttps ? https : http;
    
    const requestOptions = {
      hostname: urlObj.hostname,
      port: urlObj.port || (isHttps ? 443 : 80),
      path: urlObj.pathname + urlObj.search,
      method: options.method || 'GET',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'DangbeiProvider-TestScript/1.0',
        ...(CONFIG.apiKey && { 'Authorization': `Bearer ${CONFIG.apiKey}` }),
        ...options.headers
      },
      timeout: CONFIG.timeout
    };
    
    const req = client.request(requestOptions, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: jsonData
          });
        } catch (error) {
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: data
          });
        }
      });
    });
    
    req.on('error', (error) => {
      reject(error);
    });
    
    req.on('timeout', () => {
      req.destroy();
      reject(new Error('请求超时'));
    });
    
    if (options.body) {
      req.write(JSON.stringify(options.body));
    }
    
    req.end();
  });
}

// 测试用例定义
const testCases = [
  {
    name: '获取模型列表',
    method: 'GET',
    path: '/api/models',
    expectedStatus: 200,
    validate: (response) => {
      return response.data.success && 
             Array.isArray(response.data.data.models) &&
             response.data.data.models.length > 0;
    }
  },
  {
    name: '聊天对话测试',
    method: 'POST',
    path: '/api/chat',
    body: {
      messages: [
        {
          role: 'user',
          content: '你好，这是一个API测试。请简短回复。',
          id: 'test-msg-' + Date.now(),
          timestamp: Date.now()
        }
      ],
      model: 'gpt-4',
      stream: false,
      max_tokens: 100,
      temperature: 0.7,
      options: {
        deep_thinking: false,
        online_search: false
      }
    },
    expectedStatus: 200,
    validate: (response) => {
      return response.data.success && 
             response.data.data.message &&
             response.data.data.message.role === 'assistant';
    }
  },
  {
    name: '文本生成测试',
    method: 'POST',
    path: '/api/generate',
    body: {
      prompt: '写一句关于测试的话。',
      model: 'gpt-4',
      stream: false,
      max_tokens: 50,
      temperature: 0.5,
      task_type: 'general',
      options: {
        style: '简洁',
        format: 'plain',
        language: '中文',
        deep_thinking: false,
        online_search: false
      }
    },
    expectedStatus: 200,
    validate: (response) => {
      return response.data.success && 
             response.data.data.text &&
             response.data.data.text.length > 0;
    }
  },
  {
    name: 'OpenAI兼容接口测试',
    method: 'GET',
    path: '/v1/models',
    expectedStatus: 200,
    validate: (response) => {
      return response.data.object === 'list' && 
             Array.isArray(response.data.data) &&
             response.data.data.length > 0;
    }
  }
];

// 执行单个测试用例
async function runTestCase(testCase, retryCount = 0) {
  const startTime = Date.now();
  
  try {
    colorLog('blue', `\n🧪 执行测试: ${testCase.name}`);
    
    if (CONFIG.verbose) {
      console.log(`   方法: ${testCase.method}`);
      console.log(`   路径: ${testCase.path}`);
      if (testCase.body) {
        console.log(`   请求体: ${JSON.stringify(testCase.body, null, 2)}`);
      }
    }
    
    const response = await makeRequest(CONFIG.baseUrl + testCase.path, {
      method: testCase.method,
      body: testCase.body
    });
    
    const duration = Date.now() - startTime;
    
    // 检查状态码
    if (response.status !== testCase.expectedStatus) {
      throw new Error(`状态码不匹配: 期望 ${testCase.expectedStatus}, 实际 ${response.status}`);
    }
    
    // 执行自定义验证
    if (testCase.validate && !testCase.validate(response)) {
      throw new Error('响应数据验证失败');
    }
    
    colorLog('green', `✅ 测试通过 (${duration}ms)`);
    
    if (CONFIG.verbose) {
      console.log(`   响应状态: ${response.status}`);
      console.log(`   响应数据: ${JSON.stringify(response.data, null, 2).substring(0, 200)}...`);
    }
    
    return {
      success: true,
      duration,
      response
    };
    
  } catch (error) {
    const duration = Date.now() - startTime;
    
    if (retryCount < CONFIG.retries) {
      colorLog('yellow', `⚠️  测试失败，正在重试 (${retryCount + 1}/${CONFIG.retries}): ${error.message}`);
      await new Promise(resolve => setTimeout(resolve, 1000 * (retryCount + 1))); // 递增延迟
      return runTestCase(testCase, retryCount + 1);
    }
    
    colorLog('red', `❌ 测试失败 (${duration}ms): ${error.message}`);
    
    return {
      success: false,
      duration,
      error: error.message
    };
  }
}

// 执行所有测试
async function runAllTests() {
  colorLog('cyan', '🚀 开始执行 API 测试套件');
  colorLog('cyan', `📍 测试地址: ${CONFIG.baseUrl}`);
  colorLog('cyan', `🔑 API密钥: ${CONFIG.apiKey ? '已设置' : '未设置'}`);
  
  const results = [];
  const startTime = Date.now();
  
  for (const testCase of testCases) {
    const result = await runTestCase(testCase);
    results.push({
      name: testCase.name,
      ...result
    });
  }
  
  const totalDuration = Date.now() - startTime;
  const successCount = results.filter(r => r.success).length;
  const failureCount = results.length - successCount;
  
  // 输出测试报告
  colorLog('cyan', '\n📊 测试报告');
  console.log('='.repeat(50));
  
  results.forEach(result => {
    const status = result.success ? '✅ 通过' : '❌ 失败';
    const duration = `${result.duration}ms`;
    console.log(`${status} ${result.name.padEnd(20)} ${duration.padStart(8)}`);
    
    if (!result.success && result.error) {
      colorLog('red', `     错误: ${result.error}`);
    }
  });
  
  console.log('='.repeat(50));
  colorLog('bright', `总计: ${results.length} 个测试`);
  colorLog('green', `通过: ${successCount} 个`);
  
  if (failureCount > 0) {
    colorLog('red', `失败: ${failureCount} 个`);
  }
  
  colorLog('blue', `总耗时: ${totalDuration}ms`);
  
  // 返回退出码
  process.exit(failureCount > 0 ? 1 : 0);
}

// 显示帮助信息
function showHelp() {
  console.log(`
当贝 Provider API 测试脚本

用法:
  node api-test-script.js [选项]

选项:
  -v, --verbose     显示详细输出
  -h, --help        显示帮助信息

环境变量:
  API_BASE_URL      API基础地址 (默认: http://localhost:3000)
  API_KEY           API密钥 (可选)

示例:
  # 基础测试
  node api-test-script.js
  
  # 详细输出
  node api-test-script.js --verbose
  
  # 使用自定义地址和密钥
  API_BASE_URL=https://api.example.com API_KEY=your-key node api-test-script.js
`);
}

// 主程序入口
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  showHelp();
  process.exit(0);
}

// 捕获未处理的异常
process.on('unhandledRejection', (reason, promise) => {
  colorLog('red', `❌ 未处理的Promise拒绝: ${reason}`);
  process.exit(1);
});

process.on('uncaughtException', (error) => {
  colorLog('red', `❌ 未捕获的异常: ${error.message}`);
  process.exit(1);
});

// 启动测试
runAllTests().catch(error => {
  colorLog('red', `❌ 测试执行失败: ${error.message}`);
  process.exit(1);
});
