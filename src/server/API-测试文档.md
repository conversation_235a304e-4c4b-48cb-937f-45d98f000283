# 当贝 Provider HTTP API 测试文档

## 📋 概述

本文档提供了当贝 Provider HTTP API 的完整测试工具和参数说明。包含了交互式测试页面和详细的请求参数示例。

## 🚀 快速开始

### 1. 测试文件说明

- **`api-test-page.html`** - 交互式 API 测试页面
- **`test-api-params.json`** - API 请求参数示例文件
- **`types/api.ts`** - TypeScript 类型定义文件

### 2. 使用测试页面

1. 在浏览器中打开 `api-test-page.html`
2. 配置 API 基础地址（默认：`http://localhost:3000`）
3. 设置 API 密钥（如果需要）
4. 选择要测试的接口并填写参数
5. 点击发送请求按钮查看响应结果

## 🔧 API 接口详情

### 1. 模型列表接口

**接口地址：** `GET /api/models`

**功能：** 获取所有可用的 AI 模型列表

**请求头：**
```json
{
  "Content-Type": "application/json",
  "Authorization": "Bearer your-api-key-here"
}
```

**响应示例：**
```json
{
  "success": true,
  "data": {
    "defaultModel": "gpt-4",
    "models": [
      {
        "id": "gpt-4",
        "name": "GPT-4",
        "description": "最新的GPT-4模型，具有强大的推理能力",
        "options": [
          {
            "name": "temperature",
            "value": "0.7",
            "enabled": true,
            "selected": true
          }
        ],
        "recommended": true,
        "pinned": true,
        "icon": "https://example.com/gpt4-icon.png",
        "banner": "https://example.com/gpt4-banner.png",
        "badge": "推荐"
      }
    ],
    "total": 1
  },
  "timestamp": 1703123456789
}
```

### 2. 聊天对话接口

**接口地址：** `POST /api/chat`

**功能：** 发送聊天消息并获取 AI 回复

**请求参数：**
```json
{
  "messages": [
    {
      "role": "system",
      "content": "你是一个有用的AI助手，请用中文回答问题。",
      "id": "msg-system-001",
      "timestamp": 1703123456789
    },
    {
      "role": "user",
      "content": "你好，请介绍一下你自己。",
      "id": "msg-user-001",
      "timestamp": 1703123456790
    }
  ],
  "model": "gpt-4",
  "stream": false,
  "conversation_id": "conv-12345",
  "max_tokens": 2000,
  "temperature": 0.7,
  "options": {
    "deep_thinking": true,
    "online_search": false
  }
}
```

**参数说明：**
- `messages`: 消息列表，包含系统消息和用户消息
- `model`: 使用的模型名称
- `stream`: 是否启用流式响应（默认：false）
- `conversation_id`: 对话ID（可选）
- `max_tokens`: 最大生成令牌数（可选）
- `temperature`: 温度参数，控制创造性（0-1）
- `options.deep_thinking`: 是否启用深度思考
- `options.online_search`: 是否启用联网搜索

### 3. 文本生成接口

**接口地址：** `POST /api/generate`

**功能：** 基于提示词生成各种类型的文本内容

**请求参数：**
```json
{
  "prompt": "请为一家科技公司写一份产品介绍，产品是智能家居控制系统。",
  "model": "gpt-4",
  "stream": false,
  "max_tokens": 1500,
  "temperature": 0.6,
  "task_type": "document",
  "options": {
    "style": "专业商务",
    "format": "markdown",
    "language": "中文",
    "deep_thinking": true,
    "online_search": false
  }
}
```

**任务类型说明：**
- `creative`: 创意写作
- `code`: 代码生成
- `document`: 文档生成
- `summary`: 摘要生成
- `translation`: 翻译
- `rewrite`: 改写
- `qa`: 问答
- `general`: 通用生成

### 4. OpenAI 兼容接口

**接口地址：** `GET /v1/models`

**功能：** 提供 OpenAI API 格式的模型列表

**响应格式：**
```json
{
  "object": "list",
  "data": [
    {
      "id": "gpt-4",
      "object": "model",
      "created": 1703123456799,
      "owned_by": "dangbei-provider",
      "permission": [],
      "root": "gpt-4",
      "parent": null,
      "display_name": "GPT-4",
      "description": "最新的GPT-4模型，具有强大的推理能力",
      "context_length": 8192,
      "capabilities": {
        "chat": true,
        "completion": true,
        "embeddings": false,
        "fine_tuning": false
      },
      "pricing": {
        "input": 0.03,
        "output": 0.06
      }
    }
  ]
}
```

## 🔄 流式响应

### 聊天流式响应

启用 `stream: true` 时，响应格式为 Server-Sent Events (SSE)：

```
data: {"id":"chunk-001","object":"chat.completion.chunk","created":1703123456793,"model":"gpt-4","choices":[{"index":0,"delta":{"role":"assistant"},"finish_reason":null}]}

data: {"id":"chunk-002","object":"chat.completion.chunk","created":1703123456794,"model":"gpt-4","choices":[{"index":0,"delta":{"content":"你好！"},"finish_reason":null}]}

data: [DONE]
```

### 文本生成流式响应

```
data: {"id":"gen-chunk-001","object":"text.generation.chunk","created":1703123456797,"model":"gpt-4","choices":[{"index":0,"delta":{"text":"人工智能的发展"},"finish_reason":null}],"task_type":"summary"}

data: [DONE]
```

## ❌ 错误处理

### 常见错误类型

1. **无效请求 (INVALID_REQUEST)**
```json
{
  "success": false,
  "error": {
    "code": "INVALID_REQUEST",
    "message": "请求参数无效",
    "details": {
      "field": "messages",
      "reason": "消息列表不能为空"
    }
  },
  "timestamp": 1703123456800
}
```

2. **身份验证错误 (AUTHENTICATION_ERROR)**
```json
{
  "success": false,
  "error": {
    "code": "AUTHENTICATION_ERROR",
    "message": "身份验证失败",
    "details": {
      "reason": "API密钥无效或已过期"
    }
  },
  "timestamp": 1703123456801
}
```

3. **频率限制 (RATE_LIMIT_EXCEEDED)**
```json
{
  "success": false,
  "error": {
    "code": "RATE_LIMIT_EXCEEDED",
    "message": "请求频率超限",
    "details": {
      "limit": 100,
      "remaining": 0,
      "reset_time": 1703123516801
    }
  },
  "timestamp": 1703123456802
}
```

## 🧪 测试建议

### 1. 基础功能测试
- 测试模型列表获取
- 测试简单聊天对话
- 测试文本生成功能
- 验证错误处理机制

### 2. 高级功能测试
- 测试流式响应
- 测试不同任务类型的文本生成
- 测试深度思考和联网搜索选项
- 测试不同温度参数的效果

### 3. 性能测试
- 测试并发请求处理
- 测试长文本生成
- 测试流式响应的实时性
- 监控响应时间和资源使用

## 📝 注意事项

1. **API 密钥安全**：请妥善保管 API 密钥，不要在客户端代码中硬编码
2. **请求频率**：注意遵守 API 调用频率限制
3. **参数验证**：确保请求参数符合接口规范
4. **错误处理**：实现完善的错误处理和重试机制
5. **流式响应**：正确处理 SSE 连接的建立和断开

## 🔗 相关链接

- [TypeScript 类型定义](./types/api.ts)
- [测试参数示例](./test-api-params.json)
- [交互式测试页面](./api-test-page.html)

---

**版本：** 1.0.0  
**更新时间：** 2024-12-25  
**维护者：** 当贝 Provider 开发团队
