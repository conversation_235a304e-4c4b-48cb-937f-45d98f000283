<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API 测试页面示例</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1, h2 {
            color: #333;
        }
        .example-section {
            margin-bottom: 30px;
        }
        .example-item {
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-bottom: 15px;
        }
        .example-header {
            background: #f8f9fa;
            padding: 10px 15px;
            border-bottom: 1px solid #ddd;
            font-weight: bold;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .example-content {
            padding: 15px;
            display: none;
        }
        .example-content.active {
            display: block;
        }
        pre {
            background: #f4f4f4;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 14px;
            line-height: 1.4;
        }
        .copy-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
        }
        .copy-btn:hover {
            background: #0056b3;
        }
        .description {
            color: #666;
            margin-bottom: 10px;
            font-style: italic;
        }
        .field-info {
            background: #e9ecef;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            font-size: 14px;
        }
        .toggle-icon {
            transition: transform 0.3s;
        }
        .toggle-icon.rotated {
            transform: rotate(180deg);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 API 测试页面请求参数示例</h1>
        <p>基于 <code>src/server/types/api.ts</code> 中定义的类型生成的完整请求参数示例</p>
    </div>

    <div class="container">
        <h2>💬 聊天接口请求示例 (ChatRequest)</h2>
        
        <div class="example-section">
            <div class="example-item">
                <div class="example-header" onclick="toggleExample('chat-basic')">
                    基础聊天请求
                    <span class="toggle-icon" id="chat-basic-icon">▼</span>
                </div>
                <div class="example-content" id="chat-basic">
                    <div class="description">适用于简单的单轮对话测试</div>
                    <button class="copy-btn" onclick="copyToClipboard('chat-basic-json')">复制 JSON</button>
                    <pre id="chat-basic-json">{
  "messages": [
    {
      "role": "user",
      "content": "你好，请介绍一下你自己",
      "id": "msg_001",
      "timestamp": 1703123456789
    }
  ],
  "model": "gpt-4",
  "stream": false,
  "conversation_id": "conv_12345",
  "max_tokens": 2048,
  "temperature": 0.7
}</pre>
                    <div class="field-info">
                        <strong>关键字段说明：</strong><br>
                        • <code>messages</code>: 消息列表，包含用户和助手的对话历史<br>
                        • <code>model</code>: 使用的AI模型名称<br>
                        • <code>temperature</code>: 温度参数，控制回答的创造性（0-1）
                    </div>
                </div>
            </div>

            <div class="example-item">
                <div class="example-header" onclick="toggleExample('chat-advanced')">
                    带高级选项的聊天请求
                    <span class="toggle-icon" id="chat-advanced-icon">▼</span>
                </div>
                <div class="example-content" id="chat-advanced">
                    <div class="description">包含系统提示和高级功能选项（深度思考、联网搜索）</div>
                    <button class="copy-btn" onclick="copyToClipboard('chat-advanced-json')">复制 JSON</button>
                    <pre id="chat-advanced-json">{
  "messages": [
    {
      "role": "system",
      "content": "你是一个专业的AI助手，请用友好和专业的语气回答问题。",
      "id": "msg_system_001",
      "timestamp": 1703123456789
    },
    {
      "role": "user",
      "content": "请帮我分析一下当前的市场趋势，并提供一些投资建议。",
      "id": "msg_user_001",
      "timestamp": 1703123456790
    }
  ],
  "model": "claude-3-opus",
  "stream": true,
  "conversation_id": "conv_67890",
  "max_tokens": 4096,
  "temperature": 0.8,
  "options": {
    "deep_thinking": true,
    "online_search": true
  }
}</pre>
                    <div class="field-info">
                        <strong>高级选项说明：</strong><br>
                        • <code>deep_thinking</code>: 启用深度思考模式，提供更详细的分析<br>
                        • <code>online_search</code>: 启用联网搜索，获取最新信息<br>
                        • <code>stream</code>: 启用流式响应，实时返回结果
                    </div>
                </div>
            </div>

            <div class="example-item">
                <div class="example-header" onclick="toggleExample('chat-multi')">
                    多轮对话请求
                    <span class="toggle-icon" id="chat-multi-icon">▼</span>
                </div>
                <div class="example-content" id="chat-multi">
                    <div class="description">展示如何维持对话上下文的多轮对话</div>
                    <button class="copy-btn" onclick="copyToClipboard('chat-multi-json')">复制 JSON</button>
                    <pre id="chat-multi-json">{
  "messages": [
    {
      "role": "user",
      "content": "什么是机器学习？",
      "id": "msg_001",
      "timestamp": 1703123456789
    },
    {
      "role": "assistant",
      "content": "机器学习是人工智能的一个分支，它使计算机能够在没有明确编程的情况下学习和改进。",
      "id": "msg_002",
      "timestamp": 1703123456790
    },
    {
      "role": "user",
      "content": "能给我举个具体的例子吗？",
      "id": "msg_003",
      "timestamp": 1703123456791
    }
  ],
  "model": "gpt-3.5-turbo",
  "stream": false,
  "max_tokens": 1024,
  "temperature": 0.6
}</pre>
                    <div class="field-info">
                        <strong>多轮对话要点：</strong><br>
                        • 保持消息的时间顺序<br>
                        • 包含完整的对话历史<br>
                        • 使用相同的 conversation_id 维持上下文
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <h2>📝 文本生成接口请求示例 (TextGenerationRequest)</h2>
        
        <div class="example-section">
            <div class="example-item">
                <div class="example-header" onclick="toggleExample('text-creative')">
                    创意写作 (creative)
                    <span class="toggle-icon" id="text-creative-icon">▼</span>
                </div>
                <div class="example-content" id="text-creative">
                    <div class="description">适用于小说、诗歌、创意文案等创作任务</div>
                    <button class="copy-btn" onclick="copyToClipboard('text-creative-json')">复制 JSON</button>
                    <pre id="text-creative-json">{
  "prompt": "写一篇关于未来城市的科幻短篇小说，要求包含人工智能、环保科技和人文关怀等元素。",
  "model": "claude-3-sonnet",
  "stream": false,
  "max_tokens": 3000,
  "temperature": 0.9,
  "task_type": "creative",
  "options": {
    "style": "科幻文学",
    "format": "短篇小说",
    "language": "中文",
    "deep_thinking": true,
    "online_search": false
  }
}</pre>
                    <div class="field-info">
                        <strong>创意写作建议：</strong><br>
                        • 使用高温度参数 (0.8-1.0) 增加创造性<br>
                        • 设置较大的 max_tokens 以支持长文本生成<br>
                        • 启用 deep_thinking 获得更深入的创作思考
                    </div>
                </div>
            </div>

            <div class="example-item">
                <div class="example-header" onclick="toggleExample('text-code')">
                    代码生成 (code)
                    <span class="toggle-icon" id="text-code-icon">▼</span>
                </div>
                <div class="example-content" id="text-code">
                    <div class="description">适用于编程任务和技术文档生成</div>
                    <button class="copy-btn" onclick="copyToClipboard('text-code-json')">复制 JSON</button>
                    <pre id="text-code-json">{
  "prompt": "请用Python编写一个简单的Web爬虫，能够抓取指定网站的标题和链接，并保存到CSV文件中。要求包含错误处理和日志记录。",
  "model": "gpt-4-turbo",
  "stream": true,
  "max_tokens": 2048,
  "temperature": 0.3,
  "task_type": "code",
  "options": {
    "style": "专业代码",
    "format": "完整程序",
    "language": "Python",
    "deep_thinking": false,
    "online_search": false
  }
}</pre>
                    <div class="field-info">
                        <strong>代码生成建议：</strong><br>
                        • 使用低温度参数 (0.1-0.3) 确保代码准确性<br>
                        • 选择专门的代码模型如 gpt-4-turbo<br>
                        • 明确指定编程语言和代码风格
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <h2>⚙️ 使用建议</h2>
        <div class="field-info">
            <h3>温度参数设置建议：</h3>
            • <strong>创意写作</strong> (0.8-1.0): 需要高创造性<br>
            • <strong>代码生成</strong> (0.1-0.3): 需要准确性和逻辑性<br>
            • <strong>翻译</strong> (0.0-0.2): 需要准确性<br>
            • <strong>摘要</strong> (0.2-0.4): 需要简洁准确<br>
            • <strong>问答</strong> (0.3-0.6): 平衡准确性和表达多样性<br><br>

            <h3>令牌数设置建议：</h3>
            • <strong>短回答</strong>: 256-512 tokens<br>
            • <strong>中等回答</strong>: 1024-2048 tokens<br>
            • <strong>长回答</strong>: 2048-4096 tokens<br>
            • <strong>代码生成</strong>: 1024-3000 tokens<br>
            • <strong>创意写作</strong>: 2000-4000 tokens<br><br>

            <h3>模型选择建议：</h3>
            • <strong>GPT-4</strong>: 复杂推理、代码生成、专业写作<br>
            • <strong>GPT-3.5-turbo</strong>: 日常对话、简单任务<br>
            • <strong>Claude-3-opus</strong>: 长文本处理、深度分析<br>
            • <strong>Claude-3-sonnet</strong>: 平衡性能和成本<br>
            • <strong>Claude-3-haiku</strong>: 快速响应、简单任务
        </div>
    </div>

    <script>
        function toggleExample(id) {
            const content = document.getElementById(id);
            const icon = document.getElementById(id + '-icon');
            
            if (content.classList.contains('active')) {
                content.classList.remove('active');
                icon.classList.remove('rotated');
            } else {
                content.classList.add('active');
                icon.classList.add('rotated');
            }
        }

        function copyToClipboard(elementId) {
            const element = document.getElementById(elementId);
            const text = element.textContent;
            
            navigator.clipboard.writeText(text).then(function() {
                // 临时改变按钮文本以显示复制成功
                const button = event.target;
                const originalText = button.textContent;
                button.textContent = '已复制!';
                button.style.background = '#28a745';
                
                setTimeout(() => {
                    button.textContent = originalText;
                    button.style.background = '#007bff';
                }, 2000);
            }).catch(function(err) {
                console.error('复制失败: ', err);
                alert('复制失败，请手动复制');
            });
        }

        // 页面加载时展开第一个示例
        document.addEventListener('DOMContentLoaded', function() {
            toggleExample('chat-basic');
        });
    </script>
</body>
</html>
