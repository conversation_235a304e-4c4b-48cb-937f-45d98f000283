# 测试页面请求参数示例

本文档基于 `src/server/types/api.ts` 中定义的类型，为测试页面提供完整的请求参数示例。

## 目录

1. [聊天接口请求示例 (ChatRequest)](#聊天接口请求示例)
2. [文本生成接口请求示例 (TextGenerationRequest)](#文本生成接口请求示例)
3. [字段说明](#字段说明)
4. [使用建议](#使用建议)

## 聊天接口请求示例

### 基础聊天请求

```json
{
  "messages": [
    {
      "role": "user",
      "content": "你好，请介绍一下你自己",
      "id": "msg_001",
      "timestamp": 1703123456789
    }
  ],
  "model": "gpt-4",
  "stream": false,
  "conversation_id": "conv_12345",
  "max_tokens": 2048,
  "temperature": 0.7
}
```

**字段说明：**
- `messages`: 消息列表，包含用户和助手的对话历史
- `model`: 使用的AI模型名称
- `stream`: 是否启用流式响应（实时返回）
- `conversation_id`: 对话ID，用于维持对话上下文
- `max_tokens`: 最大生成令牌数，控制响应长度
- `temperature`: 温度参数，控制回答的创造性（0-1）

### 带高级选项的聊天请求

```json
{
  "messages": [
    {
      "role": "system",
      "content": "你是一个专业的AI助手，请用友好和专业的语气回答问题。",
      "id": "msg_system_001",
      "timestamp": 1703123456789
    },
    {
      "role": "user",
      "content": "请帮我分析一下当前的市场趋势，并提供一些投资建议。",
      "id": "msg_user_001",
      "timestamp": 1703123456790
    }
  ],
  "model": "claude-3-opus",
  "stream": true,
  "conversation_id": "conv_67890",
  "max_tokens": 4096,
  "temperature": 0.8,
  "options": {
    "deep_thinking": true,
    "online_search": true
  }
}
```

**高级选项说明：**
- `deep_thinking`: 启用深度思考模式，提供更详细的分析
- `online_search`: 启用联网搜索，获取最新信息

### 多轮对话请求

```json
{
  "messages": [
    {
      "role": "user",
      "content": "什么是机器学习？",
      "id": "msg_001",
      "timestamp": 1703123456789
    },
    {
      "role": "assistant",
      "content": "机器学习是人工智能的一个分支，它使计算机能够在没有明确编程的情况下学习和改进。",
      "id": "msg_002",
      "timestamp": 1703123456790
    },
    {
      "role": "user",
      "content": "能给我举个具体的例子吗？",
      "id": "msg_003",
      "timestamp": 1703123456791
    }
  ],
  "model": "gpt-3.5-turbo",
  "stream": false,
  "max_tokens": 1024,
  "temperature": 0.6
}
```

## 文本生成接口请求示例

### 创意写作 (creative)

```json
{
  "prompt": "写一篇关于未来城市的科幻短篇小说，要求包含人工智能、环保科技和人文关怀等元素。",
  "model": "claude-3-sonnet",
  "stream": false,
  "max_tokens": 3000,
  "temperature": 0.9,
  "task_type": "creative",
  "options": {
    "style": "科幻文学",
    "format": "短篇小说",
    "language": "中文",
    "deep_thinking": true,
    "online_search": false
  }
}
```

### 代码生成 (code)

```json
{
  "prompt": "请用Python编写一个简单的Web爬虫，能够抓取指定网站的标题和链接，并保存到CSV文件中。要求包含错误处理和日志记录。",
  "model": "gpt-4-turbo",
  "stream": true,
  "max_tokens": 2048,
  "temperature": 0.3,
  "task_type": "code",
  "options": {
    "style": "专业代码",
    "format": "完整程序",
    "language": "Python",
    "deep_thinking": false,
    "online_search": false
  }
}
```

### 文档生成 (document)

```json
{
  "prompt": "为一个在线教育平台编写产品需求文档，包括功能模块、用户角色、技术架构和项目时间线。",
  "model": "claude-3-opus",
  "stream": false,
  "max_tokens": 4096,
  "temperature": 0.5,
  "task_type": "document",
  "options": {
    "style": "正式商务",
    "format": "结构化文档",
    "language": "中文",
    "deep_thinking": true,
    "online_search": true
  }
}
```

### 摘要生成 (summary)

```json
{
  "prompt": "请总结以下文章的主要内容：[这里是一篇关于人工智能发展趋势的长篇文章内容...]",
  "model": "gpt-3.5-turbo",
  "stream": false,
  "max_tokens": 1024,
  "temperature": 0.2,
  "task_type": "summary",
  "options": {
    "style": "简洁明了",
    "format": "要点总结",
    "language": "中文",
    "deep_thinking": false,
    "online_search": false
  }
}
```

### 翻译 (translation)

```json
{
  "prompt": "请将以下英文文本翻译成中文：'Artificial intelligence is revolutionizing the way we work, learn, and interact with technology. From healthcare to finance, AI applications are becoming increasingly sophisticated and widespread.'",
  "model": "gpt-4",
  "stream": false,
  "max_tokens": 512,
  "temperature": 0.1,
  "task_type": "translation",
  "options": {
    "style": "准确流畅",
    "format": "直译",
    "language": "中文",
    "deep_thinking": false,
    "online_search": false
  }
}
```

### 改写 (rewrite)

```json
{
  "prompt": "请将以下文本改写得更加正式和专业：'这个项目挺不错的，我们应该考虑一下，可能会有很好的效果。'",
  "model": "claude-3-sonnet",
  "stream": false,
  "max_tokens": 256,
  "temperature": 0.4,
  "task_type": "rewrite",
  "options": {
    "style": "正式专业",
    "format": "商务语言",
    "language": "中文",
    "deep_thinking": false,
    "online_search": false
  }
}
```

### 问答 (qa)

```json
{
  "prompt": "什么是区块链技术？它有哪些主要应用场景？",
  "model": "gpt-4-turbo",
  "stream": false,
  "max_tokens": 1536,
  "temperature": 0.3,
  "task_type": "qa",
  "options": {
    "style": "教育性",
    "format": "问答形式",
    "language": "中文",
    "deep_thinking": true,
    "online_search": true
  }
}
```

### 通用生成 (general)

```json
{
  "prompt": "请为我制定一个为期一个月的健身计划，包括有氧运动、力量训练和饮食建议。",
  "model": "claude-3-haiku",
  "stream": false,
  "max_tokens": 2048,
  "temperature": 0.6,
  "task_type": "general",
  "options": {
    "style": "实用指导",
    "format": "计划表",
    "language": "中文",
    "deep_thinking": false,
    "online_search": false
  }
}
```

## 字段说明

### 通用字段

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| `model` | string | 是 | 使用的AI模型名称 |
| `stream` | boolean | 否 | 是否启用流式响应，默认false |
| `max_tokens` | number | 否 | 最大生成令牌数 |
| `temperature` | number | 否 | 温度参数，控制创造性，范围0-1 |

### 聊天接口特有字段

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| `messages` | ChatMessage[] | 是 | 消息列表 |
| `conversation_id` | string | 否 | 对话ID |
| `options` | object | 否 | 聊天选项 |

### 文本生成接口特有字段

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| `prompt` | string | 是 | 生成提示词 |
| `task_type` | TextGenerationTaskType | 否 | 任务类型 |
| `options` | object | 否 | 生成选项 |

### 消息对象 (ChatMessage)

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| `role` | 'user' \| 'assistant' \| 'system' | 是 | 消息角色 |
| `content` | string | 是 | 消息内容 |
| `id` | string | 否 | 消息ID |
| `timestamp` | number | 否 | 时间戳 |

### 任务类型 (TextGenerationTaskType)

- `creative`: 创意写作
- `code`: 代码生成
- `document`: 文档生成
- `summary`: 摘要生成
- `translation`: 翻译
- `rewrite`: 改写
- `qa`: 问答
- `general`: 通用生成

## 使用建议

### 温度参数设置建议

- **创意写作** (0.8-1.0): 需要高创造性
- **代码生成** (0.1-0.3): 需要准确性和逻辑性
- **翻译** (0.0-0.2): 需要准确性
- **摘要** (0.2-0.4): 需要简洁准确
- **问答** (0.3-0.6): 平衡准确性和表达多样性

### 令牌数设置建议

- **短回答**: 256-512 tokens
- **中等回答**: 1024-2048 tokens
- **长回答**: 2048-4096 tokens
- **代码生成**: 1024-3000 tokens
- **创意写作**: 2000-4000 tokens

### 模型选择建议

- **GPT-4**: 复杂推理、代码生成、专业写作
- **GPT-3.5-turbo**: 日常对话、简单任务
- **Claude-3-opus**: 长文本处理、深度分析
- **Claude-3-sonnet**: 平衡性能和成本
- **Claude-3-haiku**: 快速响应、简单任务

这些示例可以直接在测试页面中使用，根据具体需求调整参数值。
