/**
 * HTTP API 测试脚本
 * 测试当贝AI Provider HTTP API的基本功能
 */

const axios = require('axios');

// API 基础URL
const BASE_URL = 'http://localhost:3000';

/**
 * 测试健康检查接口
 */
async function testHealthCheck() {
  console.log('\n🔍 测试健康检查接口...');
  try {
    const response = await axios.get(`${BASE_URL}/health`);
    console.log('✅ 健康检查成功:', response.data.data.status);
    return true;
  } catch (error) {
    console.error('❌ 健康检查失败:', error.message);
    return false;
  }
}

/**
 * 测试模型列表接口
 */
async function testModelsList() {
  console.log('\n🔍 测试模型列表接口...');
  try {
    const response = await axios.get(`${BASE_URL}/api/models`);
    const { data } = response.data;
    
    console.log(`✅ 获取模型列表成功: ${data.total} 个模型`);
    console.log(`📋 默认模型: ${data.defaultModel}`);
    
    // 显示前3个模型
    console.log('🤖 前3个模型:');
    data.models.slice(0, 3).forEach((model, index) => {
      console.log(`   ${index + 1}. ${model.name} (${model.id})`);
      console.log(`      描述: ${model.description}`);
      console.log(`      推荐: ${model.recommended ? '是' : '否'}, 置顶: ${model.pinned ? '是' : '否'}`);
    });
    
    return data.models;
  } catch (error) {
    console.error('❌ 获取模型列表失败:', error.message);
    return null;
  }
}

/**
 * 测试特定模型信息接口
 */
async function testModelInfo(modelId) {
  console.log(`\n🔍 测试模型信息接口 (${modelId})...`);
  try {
    const response = await axios.get(`${BASE_URL}/api/models/${modelId}`);
    const model = response.data.data;
    
    console.log(`✅ 获取模型信息成功: ${model.name}`);
    console.log(`📝 描述: ${model.description}`);
    console.log(`⚙️ 选项数量: ${model.options.length}`);
    
    return model;
  } catch (error) {
    console.error(`❌ 获取模型信息失败 (${modelId}):`, error.message);
    return null;
  }
}

/**
 * 测试聊天接口（非流式）
 */
async function testChatNonStream(modelId) {
  console.log(`\n🔍 测试聊天接口 (非流式, ${modelId})...`);
  try {
    const requestData = {
      messages: [
        {
          role: 'user',
          content: '你好，请用一句话介绍你自己'
        }
      ],
      model: modelId,
      stream: false
    };
    
    console.log('📤 发送聊天请求...');
    const startTime = Date.now();
    
    const response = await axios.post(`${BASE_URL}/api/chat`, requestData, {
      timeout: 30000 // 30秒超时
    });
    
    const duration = Date.now() - startTime;
    const { data } = response.data;
    
    console.log(`✅ 聊天请求成功 (耗时: ${duration}ms)`);
    console.log(`🤖 模型: ${data.model}`);
    console.log(`💬 回复: ${data.message.content.substring(0, 100)}${data.message.content.length > 100 ? '...' : ''}`);
    console.log(`🆔 对话ID: ${data.conversation_id}`);
    console.log(`📝 消息ID: ${data.message_id}`);
    
    return data;
  } catch (error) {
    console.error(`❌ 聊天请求失败 (${modelId}):`, error.message);
    if (error.response) {
      console.error('📋 错误详情:', error.response.data);
    }
    return null;
  }
}

/**
 * 测试API信息接口
 */
async function testApiInfo() {
  console.log('\n🔍 测试API信息接口...');
  try {
    const response = await axios.get(`${BASE_URL}/api/info`);
    const { data } = response.data;
    
    console.log(`✅ 获取API信息成功: ${data.name}`);
    console.log(`📦 版本: ${data.version}`);
    console.log(`📋 端点数量: ${Object.keys(data.endpoints).length}`);
    
    return data;
  } catch (error) {
    console.error('❌ 获取API信息失败:', error.message);
    return null;
  }
}

/**
 * 测试错误处理
 */
async function testErrorHandling() {
  console.log('\n🔍 测试错误处理...');
  
  // 测试不存在的模型
  try {
    await axios.get(`${BASE_URL}/api/models/nonexistent-model`);
    console.log('❌ 应该返回404错误');
  } catch (error) {
    if (error.response && error.response.status === 404) {
      console.log('✅ 404错误处理正确');
    } else {
      console.log('❌ 错误处理不正确:', error.message);
    }
  }
  
  // 测试无效的聊天请求
  try {
    await axios.post(`${BASE_URL}/api/chat`, {
      messages: [],
      model: 'invalid-model'
    });
    console.log('❌ 应该返回400错误');
  } catch (error) {
    if (error.response && error.response.status === 400) {
      console.log('✅ 400错误处理正确');
    } else {
      console.log('❌ 错误处理不正确:', error.message);
    }
  }
}

/**
 * 主测试函数
 */
async function runTests() {
  console.log('🚀 开始测试当贝AI Provider HTTP API...');
  console.log(`🌐 API地址: ${BASE_URL}`);
  
  // 1. 测试健康检查
  const healthOk = await testHealthCheck();
  if (!healthOk) {
    console.log('❌ 服务器未就绪，请先启动服务器');
    process.exit(1);
  }
  
  // 2. 测试API信息
  await testApiInfo();
  
  // 3. 测试模型列表
  const models = await testModelsList();
  if (!models || models.length === 0) {
    console.log('❌ 无法获取模型列表');
    return;
  }
  
  // 4. 测试特定模型信息
  const firstModel = models[0];
  await testModelInfo(firstModel.id);
  
  // 5. 测试聊天接口（选择一个推荐的模型）
  const recommendedModel = models.find(m => m.recommended) || firstModel;
  console.log(`\n🎯 选择模型进行聊天测试: ${recommendedModel.name} (${recommendedModel.id})`);
  
  await testChatNonStream(recommendedModel.id);
  
  // 6. 测试错误处理
  await testErrorHandling();
  
  console.log('\n🎉 所有测试完成！');
}

// 运行测试
if (require.main === module) {
  runTests().catch(error => {
    console.error('❌ 测试运行失败:', error);
    process.exit(1);
  });
}

module.exports = {
  testHealthCheck,
  testModelsList,
  testModelInfo,
  testChatNonStream,
  testApiInfo,
  testErrorHandling,
  runTests
};
