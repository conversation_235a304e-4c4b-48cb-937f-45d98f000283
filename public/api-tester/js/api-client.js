/**
 * API客户端模块
 * 处理HTTP请求和响应
 */

// API客户端类
class ApiClient {
  constructor() {
    this.baseUrl = window.location.origin;
    this.defaultHeaders = {
      'Content-Type': 'application/json'
    };
    this.requestHistory = [];
    this.maxHistorySize = 50;
    this.currentRequest = null;
    this.eventSource = null;
  }

  /**
   * 设置基础URL
   * @param {string} url - 基础URL
   */
  setBaseUrl(url) {
    this.baseUrl = url.replace(/\/$/, ''); // 移除末尾斜杠
  }

  /**
   * 获取完整URL
   * @param {string} path - 路径
   * @returns {string} 完整URL
   */
  getFullUrl(path) {
    if (path.startsWith('http://') || path.startsWith('https://')) {
      return path;
    }
    return this.baseUrl + (path.startsWith('/') ? path : '/' + path);
  }

  /**
   * 发送HTTP请求
   * @param {object} options - 请求选项
   * @returns {Promise<object>} 响应结果
   */
  async sendRequest(options) {
    const {
      method = 'GET',
      url,
      headers = {},
      params = {},
      body = null,
      timeout = 30000
    } = options;

    const startTime = Date.now();
    const requestId = Utils.generateId();

    try {
      // 构建完整URL
      let fullUrl = this.getFullUrl(url);
      
      // 添加URL参数
      if (Object.keys(params).length > 0) {
        const paramString = Utils.buildUrlParams(params);
        if (paramString) {
          fullUrl += (fullUrl.includes('?') ? '&' : '?') + paramString;
        }
      }

      // 合并请求头
      const requestHeaders = {
        ...this.defaultHeaders,
        ...headers
      };

      // 构建请求配置
      const requestConfig = {
        method: method.toUpperCase(),
        headers: requestHeaders,
        signal: AbortSignal.timeout(timeout)
      };

      // 添加请求体
      if (body && method.toUpperCase() !== 'GET' && method.toUpperCase() !== 'HEAD') {
        if (typeof body === 'string') {
          requestConfig.body = body;
        } else if (body instanceof FormData) {
          requestConfig.body = body;
          // FormData会自动设置Content-Type，需要删除手动设置的
          delete requestHeaders['Content-Type'];
        } else {
          requestConfig.body = JSON.stringify(body);
        }
      }

      console.log(`[${requestId}] 发送请求:`, {
        method: requestConfig.method,
        url: fullUrl,
        headers: requestHeaders,
        body: requestConfig.body
      });

      // 发送请求
      const response = await fetch(fullUrl, requestConfig);
      const endTime = Date.now();
      const responseTime = endTime - startTime;

      // 获取响应头
      const responseHeaders = {};
      response.headers.forEach((value, key) => {
        responseHeaders[key] = value;
      });

      // 获取响应体
      let responseBody;
      let responseSize = 0;
      
      const contentType = response.headers.get('content-type') || '';
      
      if (contentType.includes('application/json')) {
        const text = await response.text();
        responseSize = new Blob([text]).size;
        try {
          responseBody = JSON.parse(text);
        } catch {
          responseBody = text;
        }
      } else if (contentType.includes('text/')) {
        responseBody = await response.text();
        responseSize = new Blob([responseBody]).size;
      } else {
        const arrayBuffer = await response.arrayBuffer();
        responseSize = arrayBuffer.byteLength;
        responseBody = `[Binary data: ${Utils.formatFileSize(responseSize)}]`;
      }

      // 构建响应结果
      const result = {
        requestId,
        request: {
          method: requestConfig.method,
          url: fullUrl,
          headers: requestHeaders,
          body: requestConfig.body,
          timestamp: startTime
        },
        response: {
          status: response.status,
          statusText: response.statusText,
          headers: responseHeaders,
          body: responseBody,
          size: responseSize,
          time: responseTime,
          timestamp: endTime
        },
        success: response.ok
      };

      console.log(`[${requestId}] 请求完成:`, result);

      // 添加到历史记录
      this.addToHistory(result);

      return result;

    } catch (error) {
      const endTime = Date.now();
      const responseTime = endTime - startTime;

      console.error(`[${requestId}] 请求失败:`, error);

      const result = {
        requestId,
        request: {
          method: method.toUpperCase(),
          url: this.getFullUrl(url),
          headers: { ...this.defaultHeaders, ...headers },
          body,
          timestamp: startTime
        },
        response: null,
        error: {
          name: error.name,
          message: error.message,
          time: responseTime,
          timestamp: endTime
        },
        success: false
      };

      // 添加到历史记录
      this.addToHistory(result);

      throw result;
    }
  }

  /**
   * 发送流式请求（Server-Sent Events）
   * @param {object} options - 请求选项
   * @param {Function} onMessage - 消息回调
   * @param {Function} onError - 错误回调
   * @param {Function} onComplete - 完成回调
   * @returns {Function} 停止函数
   */
  sendStreamRequest(options, onMessage, onError, onComplete) {
    const {
      method = 'POST',
      url,
      headers = {},
      params = {},
      body = null
    } = options;

    // 构建完整URL
    let fullUrl = this.getFullUrl(url);
    
    // 添加URL参数
    if (Object.keys(params).length > 0) {
      const paramString = Utils.buildUrlParams(params);
      if (paramString) {
        fullUrl += (fullUrl.includes('?') ? '&' : '?') + paramString;
      }
    }

    const requestId = Utils.generateId();
    const startTime = Date.now();

    console.log(`[${requestId}] 开始流式请求:`, { method, url: fullUrl });

    // 如果是POST请求，需要先发送请求体
    if (method.toUpperCase() === 'POST' && body) {
      // 对于流式请求，通常需要在URL中添加stream=true参数
      const streamParams = { ...params, stream: true };
      const streamParamString = Utils.buildUrlParams(streamParams);
      fullUrl = this.getFullUrl(url) + (fullUrl.includes('?') ? '&' : '?') + streamParamString;
      
      // 发送POST请求启动流式响应
      fetch(fullUrl, {
        method: 'POST',
        headers: {
          ...this.defaultHeaders,
          ...headers,
          'Accept': 'text/event-stream',
          'Cache-Control': 'no-cache'
        },
        body: typeof body === 'string' ? body : JSON.stringify(body)
      }).then(response => {
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        // 创建EventSource来接收流式数据
        this.eventSource = new EventSource(fullUrl);
        this.setupEventSource(this.eventSource, requestId, startTime, onMessage, onError, onComplete);
      }).catch(error => {
        console.error(`[${requestId}] 流式请求启动失败:`, error);
        if (onError) onError(error);
      });
    } else {
      // 直接创建EventSource
      this.eventSource = new EventSource(fullUrl);
      this.setupEventSource(this.eventSource, requestId, startTime, onMessage, onError, onComplete);
    }

    // 返回停止函数
    return () => this.stopStreamRequest();
  }

  /**
   * 设置EventSource事件监听
   * @private
   */
  setupEventSource(eventSource, requestId, startTime, onMessage, onError, onComplete) {
    eventSource.onopen = () => {
      console.log(`[${requestId}] 流式连接已建立`);
    };

    eventSource.onmessage = (event) => {
      try {
        const data = event.data;
        console.log(`[${requestId}] 收到流式数据:`, data);
        
        if (data === '[DONE]') {
          console.log(`[${requestId}] 流式请求完成`);
          this.stopStreamRequest();
          if (onComplete) onComplete();
          return;
        }

        let parsedData;
        try {
          parsedData = JSON.parse(data);
        } catch {
          parsedData = data;
        }

        if (onMessage) onMessage(parsedData, event);
      } catch (error) {
        console.error(`[${requestId}] 流式数据处理失败:`, error);
        if (onError) onError(error);
      }
    };

    eventSource.onerror = (error) => {
      console.error(`[${requestId}] 流式请求错误:`, error);
      this.stopStreamRequest();
      if (onError) onError(error);
    };
  }

  /**
   * 停止流式请求
   */
  stopStreamRequest() {
    if (this.eventSource) {
      this.eventSource.close();
      this.eventSource = null;
      console.log('流式请求已停止');
    }
  }

  /**
   * 添加到历史记录
   * @param {object} result - 请求结果
   * @private
   */
  addToHistory(result) {
    this.requestHistory.unshift(result);
    
    // 限制历史记录大小
    if (this.requestHistory.length > this.maxHistorySize) {
      this.requestHistory = this.requestHistory.slice(0, this.maxHistorySize);
    }

    // 保存到本地存储
    Utils.storage.set('api_request_history', this.requestHistory);
  }

  /**
   * 获取历史记录
   * @returns {Array} 历史记录列表
   */
  getHistory() {
    return this.requestHistory;
  }

  /**
   * 清空历史记录
   */
  clearHistory() {
    this.requestHistory = [];
    Utils.storage.remove('api_request_history');
  }

  /**
   * 从本地存储加载历史记录
   */
  loadHistory() {
    const history = Utils.storage.get('api_request_history', []);
    this.requestHistory = history.slice(0, this.maxHistorySize);
  }

  /**
   * 获取API信息
   * @returns {Promise<object>} API信息
   */
  async getApiInfo() {
    try {
      const result = await this.sendRequest({
        method: 'GET',
        url: '/api/info'
      });
      return result.response.body;
    } catch (error) {
      console.error('获取API信息失败:', error);
      throw error;
    }
  }

  /**
   * 健康检查
   * @returns {Promise<object>} 健康状态
   */
  async healthCheck() {
    try {
      const result = await this.sendRequest({
        method: 'GET',
        url: '/health'
      });
      return result.response.body;
    } catch (error) {
      console.error('健康检查失败:', error);
      throw error;
    }
  }
}

// 等待DOM加载完成后创建全局API客户端实例
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    window.apiClient = new ApiClient();
  });
} else {
  // DOM已经加载完成
  window.apiClient = new ApiClient();
}

// 导出API客户端类（如果支持模块化）
if (typeof module !== 'undefined' && module.exports) {
  module.exports = ApiClient;
}
