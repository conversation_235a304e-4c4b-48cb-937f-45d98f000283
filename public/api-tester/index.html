<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>当贝AI Provider - HTTP API 测试工具</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🧪</text></svg>">
</head>
<body>
    <!-- 顶部导航栏 -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <span class="logo-icon">🧪</span>
                    <h1>当贝AI Provider API 测试工具</h1>
                </div>
                <div class="header-actions">
                    <button id="theme-toggle" class="btn btn-icon" title="切换主题">
                        <span class="theme-icon">🌙</span>
                    </button>
                    <button id="clear-history" class="btn btn-secondary" title="清除历史记录">
                        清除历史
                    </button>
                    <button id="export-config" class="btn btn-secondary" title="导出配置">
                        导出配置
                    </button>
                    <input type="file" id="import-config" accept=".json" style="display: none;">
                    <button id="import-config-btn" class="btn btn-secondary" title="导入配置">
                        导入配置
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="main">
        <div class="container">
            <div class="layout">
                <!-- 左侧边栏 - 接口列表 -->
                <aside class="sidebar">
                    <div class="sidebar-header">
                        <h2>API 接口列表</h2>
                        <button id="refresh-apis" class="btn btn-icon" title="刷新接口列表">
                            🔄
                        </button>
                    </div>
                    
                    <!-- 接口搜索 -->
                    <div class="search-box">
                        <input type="text" id="api-search" placeholder="搜索接口..." class="search-input">
                    </div>

                    <!-- 接口分组 -->
                    <div class="api-groups" id="api-groups">
                        <!-- 动态生成的接口列表 -->
                    </div>

                    <!-- 历史记录 -->
                    <div class="history-section">
                        <h3>请求历史</h3>
                        <div class="history-list" id="history-list">
                            <!-- 动态生成的历史记录 -->
                        </div>
                    </div>
                </aside>

                <!-- 主要内容区域 -->
                <div class="content">
                    <!-- 请求构建器 -->
                    <section class="request-builder">
                        <div class="section-header">
                            <h2>请求构建器</h2>
                            <div class="section-actions">
                                <button id="load-template" class="btn btn-secondary">加载模板</button>
                                <button id="save-template" class="btn btn-secondary">保存模板</button>
                                <button id="clear-request" class="btn btn-secondary">清空</button>
                            </div>
                        </div>

                        <!-- 基本请求信息 -->
                        <div class="request-basic">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="request-method">HTTP 方法</label>
                                    <select id="request-method" class="form-control">
                                        <option value="GET">GET</option>
                                        <option value="POST">POST</option>
                                        <option value="PUT">PUT</option>
                                        <option value="DELETE">DELETE</option>
                                        <option value="PATCH">PATCH</option>
                                        <option value="HEAD">HEAD</option>
                                        <option value="OPTIONS">OPTIONS</option>
                                    </select>
                                </div>
                                <div class="form-group flex-1">
                                    <label for="request-url">请求 URL</label>
                                    <input type="text" id="request-url" class="form-control" placeholder="http://localhost:3000/api/models" value="http://localhost:3000">
                                </div>
                                <div class="form-group">
                                    <button id="send-request" class="btn btn-primary">发送请求</button>
                                </div>
                            </div>
                        </div>

                        <!-- 请求选项卡 -->
                        <div class="tabs">
                            <div class="tab-headers">
                                <button class="tab-header active" data-tab="headers">请求头</button>
                                <button class="tab-header" data-tab="params">URL 参数</button>
                                <button class="tab-header" data-tab="body">请求体</button>
                                <button class="tab-header" data-tab="auth">认证</button>
                            </div>

                            <!-- 请求头标签页 -->
                            <div class="tab-content active" data-tab="headers">
                                <div class="headers-editor">
                                    <div class="editor-toolbar">
                                        <button id="add-header" class="btn btn-small">添加请求头</button>
                                        <button id="clear-headers" class="btn btn-small btn-secondary">清空</button>
                                    </div>
                                    <div class="headers-list" id="headers-list">
                                        <!-- 动态生成的请求头列表 -->
                                    </div>
                                </div>
                            </div>

                            <!-- URL 参数标签页 -->
                            <div class="tab-content" data-tab="params">
                                <div class="params-editor">
                                    <div class="editor-toolbar">
                                        <button id="add-param" class="btn btn-small">添加参数</button>
                                        <button id="clear-params" class="btn btn-small btn-secondary">清空</button>
                                    </div>
                                    <div class="params-list" id="params-list">
                                        <!-- 动态生成的参数列表 -->
                                    </div>
                                </div>
                            </div>

                            <!-- 请求体标签页 -->
                            <div class="tab-content" data-tab="body">
                                <div class="body-editor">
                                    <div class="editor-toolbar">
                                        <select id="body-type" class="form-control">
                                            <option value="none">无请求体</option>
                                            <option value="json">JSON</option>
                                            <option value="form">表单数据</option>
                                            <option value="text">纯文本</option>
                                            <option value="xml">XML</option>
                                        </select>
                                        <button id="format-json" class="btn btn-small">格式化 JSON</button>
                                        <button id="clear-body" class="btn btn-small btn-secondary">清空</button>
                                    </div>
                                    <div class="body-content">
                                        <textarea id="request-body" class="form-control code-editor" placeholder="请输入请求体内容..." rows="10"></textarea>
                                    </div>
                                </div>
                            </div>

                            <!-- 认证标签页 -->
                            <div class="tab-content" data-tab="auth">
                                <div class="auth-editor">
                                    <div class="form-group">
                                        <label for="auth-type">认证类型</label>
                                        <select id="auth-type" class="form-control">
                                            <option value="none">无认证</option>
                                            <option value="bearer">Bearer Token</option>
                                            <option value="basic">Basic Auth</option>
                                            <option value="api-key">API Key</option>
                                        </select>
                                    </div>
                                    <div id="auth-fields">
                                        <!-- 动态生成的认证字段 -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>

                    <!-- 响应显示器 -->
                    <section class="response-viewer">
                        <div class="section-header">
                            <h2>响应结果</h2>
                            <div class="section-actions">
                                <button id="copy-response" class="btn btn-secondary">复制响应</button>
                                <button id="download-response" class="btn btn-secondary">下载响应</button>
                                <button id="clear-response" class="btn btn-secondary">清空</button>
                            </div>
                        </div>

                        <!-- 响应状态 -->
                        <div class="response-status" id="response-status" style="display: none;">
                            <div class="status-info">
                                <span class="status-code" id="status-code">200</span>
                                <span class="status-text" id="status-text">OK</span>
                                <span class="response-time" id="response-time">123ms</span>
                                <span class="response-size" id="response-size">1.2KB</span>
                            </div>
                        </div>

                        <!-- 响应选项卡 -->
                        <div class="tabs">
                            <div class="tab-headers">
                                <button class="tab-header active" data-tab="response-body">响应体</button>
                                <button class="tab-header" data-tab="response-headers">响应头</button>
                                <button class="tab-header" data-tab="response-stream">流式响应</button>
                            </div>

                            <!-- 响应体标签页 -->
                            <div class="tab-content active" data-tab="response-body">
                                <div class="response-body-viewer">
                                    <div class="viewer-toolbar">
                                        <select id="response-format" class="form-control">
                                            <option value="json">JSON</option>
                                            <option value="text">纯文本</option>
                                            <option value="html">HTML</option>
                                            <option value="xml">XML</option>
                                        </select>
                                        <button id="format-response" class="btn btn-small">格式化</button>
                                        <button id="wrap-text" class="btn btn-small">换行</button>
                                    </div>
                                    <div class="response-content">
                                        <pre id="response-body" class="code-viewer">等待响应...</pre>
                                    </div>
                                </div>
                            </div>

                            <!-- 响应头标签页 -->
                            <div class="tab-content" data-tab="response-headers">
                                <div class="response-headers-viewer">
                                    <pre id="response-headers" class="code-viewer">等待响应...</pre>
                                </div>
                            </div>

                            <!-- 流式响应标签页 -->
                            <div class="tab-content" data-tab="response-stream">
                                <div class="stream-viewer">
                                    <div class="stream-toolbar">
                                        <button id="start-stream" class="btn btn-small">开始流式请求</button>
                                        <button id="stop-stream" class="btn btn-small btn-secondary">停止</button>
                                        <button id="clear-stream" class="btn btn-small btn-secondary">清空</button>
                                        <label class="stream-option">
                                            <input type="checkbox" id="enable-sse-styling" checked>
                                            启用样式区分
                                        </label>
                                        <span class="stream-status" id="stream-status">未连接</span>
                                    </div>
                                    <div class="stream-content">
                                        <div id="stream-messages" class="stream-messages">
                                            <!-- SSE 消息将在这里动态生成 -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>
                </div>
            </div>
        </div>
    </main>

    <!-- 模态框 -->
    <div id="modal-overlay" class="modal-overlay" style="display: none;">
        <div class="modal">
            <div class="modal-header">
                <h3 id="modal-title">模态框标题</h3>
                <button id="modal-close" class="btn btn-icon">✕</button>
            </div>
            <div class="modal-body" id="modal-body">
                <!-- 动态内容 -->
            </div>
            <div class="modal-footer">
                <button id="modal-cancel" class="btn btn-secondary">取消</button>
                <button id="modal-confirm" class="btn btn-primary">确认</button>
            </div>
        </div>
    </div>

    <!-- 加载指示器 -->
    <div id="loading-indicator" class="loading-indicator" style="display: none;">
        <div class="loading-spinner"></div>
        <div class="loading-text">正在处理请求...</div>
    </div>

    <!-- 通知容器 -->
    <div id="notifications" class="notifications"></div>

    <!-- JavaScript 文件 -->
    <script src="js/utils.js"></script>
    <script src="js/api-client.js"></script>
    <script src="js/ui-components.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
