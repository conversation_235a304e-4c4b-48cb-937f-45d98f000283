# 当贝AI Provider SDK - 技术架构文档

## 🏗️ 整体架构

当贝AI Provider SDK采用分层架构设计，从上到下分为5个主要层次，每层职责明确，接口清晰。

## 📊 架构图

```mermaid
graph TB
    subgraph "用户应用层"
        APP[用户应用]
    end
    
    subgraph "Provider层 - 用户接口"
        DP[DangbeiProvider]
        DP --> |提供高级API| APP
    end
    
    subgraph "Service层 - 业务逻辑"
        CS[ConversationService]
        CHS[ChatService]
        COS[CommonService]
        
        DP --> CS
        DP --> CHS
        DP --> COS
    end
    
    subgraph "Client层 - 网络通信"
        HC[HttpClient]
        SSE[SSEClient]
        
        CS --> HC
        CHS --> HC
        CHS --> SSE
        COS --> HC
    end
    
    subgraph "Utils层 - 工具函数"
        SU[SignatureUtils]
        DU[DeviceUtils]
        V2E[V2ErrorHandler]
        
        HC --> SU
        HC --> DU
        HC --> V2E
    end
    
    subgraph "WebAssembly层 - 高性能计算"
        WASM[WASM Module]
        FB[Fallback Signature]
        US[Unified Signature]
        
        SU --> US
        US --> WASM
        US --> FB
    end
    
    subgraph "外部服务"
        API[当贝AI API]
        HC --> API
        SSE --> API
    end
```

## 🔧 核心组件详解

### 1. Provider层 - 用户接口

#### DangbeiProvider
- **职责**: 提供统一的高级API接口
- **功能**: 
  - 快速聊天 (`quickChat`)
  - 流式聊天 (`chat`)
  - 同步聊天 (`chatSync`)
  - 对话管理 (`createConversation`)
  - 设备管理 (`getDeviceConfig`, `updateDeviceConfig`)

```typescript
// 用户接口示例
const provider = new DangbeiProvider({
  debug: true,
  timeout: 30000
});

const response = await provider.quickChat('你好');
```

### 2. Service层 - 业务逻辑

#### ConversationService
- **职责**: 对话会话管理
- **功能**: 创建对话、管理对话状态
- **接口**: `createConversation(options)`

#### ChatService  
- **职责**: 聊天消息处理
- **功能**: 发送消息、处理流式响应
- **接口**: `chat(options)`, `chatSync(options)`

#### CommonService
- **职责**: 通用功能服务
- **功能**: ID生成、服务状态检查
- **接口**: `generateId()`, `checkServiceAvailability()`

### 3. Client层 - 网络通信

#### HttpClient
- **职责**: HTTP请求处理
- **功能**: 
  - 自动签名生成
  - 请求重试机制
  - 错误处理和分类
  - 调试日志输出

```typescript
// HTTP客户端配置
const httpClient = new HttpClient({
  deviceId: 'device-id',
  timeout: 30000,
  retries: 3,
  debug: true
});
```

#### SSEClient
- **职责**: Server-Sent Events处理
- **功能**:
  - 流式响应解析
  - 实时消息回调
  - 连接管理和重连
  - 资源清理

### 4. Utils层 - 工具函数

#### SignatureUtils
- **职责**: 签名生成和验证
- **功能**:
  - v1接口MD5签名
  - v2接口WebAssembly签名
  - 参数排序和编码
  - 签名验证

#### DeviceUtils
- **职责**: 设备管理
- **功能**:
  - 设备ID生成 (`hash_suffix`格式)
  - 设备配置管理
  - User-Agent生成

#### V2ErrorHandler
- **职责**: v2接口错误处理
- **功能**:
  - 错误分类和识别
  - 详细错误信息提供
  - 解决建议生成

### 5. WebAssembly层 - 高性能计算

#### WASM Module (dangbei-signature-wasm.js)
- **职责**: 原生WebAssembly签名算法
- **功能**:
  - 高性能签名计算
  - 内存管理
  - 原生算法调用

#### Fallback Signature (fallback-signature.js)
- **职责**: JavaScript备用实现
- **功能**:
  - 纯JS签名算法
  - WASM失败时的降级方案
  - 兼容性保证

#### Unified Signature (unified-signature.js)
- **职责**: 统一签名接口
- **功能**:
  - 自动选择最佳实现
  - 性能监控和切换
  - 批量签名处理

## 🔄 数据流图

```mermaid
sequenceDiagram
    participant User as 用户应用
    participant Provider as DangbeiProvider
    participant Service as ChatService
    participant Client as HttpClient
    participant Utils as SignatureUtils
    participant WASM as WebAssembly
    participant API as 当贝AI API

    User->>Provider: quickChat("你好")
    Provider->>Service: createConversation()
    Service->>Client: POST /v1/batch/create
    Client->>Utils: generateSignature()
    Utils->>WASM: 计算v2签名
    WASM-->>Utils: 返回签名结果
    Utils-->>Client: 签名完成
    Client->>API: 发送HTTP请求
    API-->>Client: 返回对话ID
    Client-->>Service: 对话创建成功
    Service-->>Provider: 返回对话信息
    
    Provider->>Service: chat(conversationId, "你好")
    Service->>Client: POST /v2/chat (SSE)
    Client->>Utils: generateSignature()
    Utils->>WASM: 计算v2签名
    WASM-->>Utils: 返回签名结果
    Client->>API: 建立SSE连接
    
    loop 流式响应
        API-->>Client: SSE消息片段
        Client-->>Service: 处理消息
        Service-->>Provider: 触发回调
        Provider-->>User: onMessage回调
    end
    
    API-->>Client: 完成信号
    Client-->>Service: 聊天完成
    Service-->>Provider: 返回完整响应
    Provider-->>User: 返回结果
```

## 🔐 签名算法架构

```mermaid
graph LR
    subgraph "签名请求"
        REQ[请求数据]
        NONCE[Nonce]
        TS[Timestamp]
    end
    
    subgraph "签名选择器"
        US[Unified Signature]
        US --> |检测接口版本| V1{v1接口?}
        US --> |检测接口版本| V2{v2接口?}
    end
    
    subgraph "v1签名算法"
        V1 --> MD5[MD5 Hash]
        MD5 --> V1SIG[v1签名]
    end
    
    subgraph "v2签名算法"
        V2 --> WASM_CHECK{WASM可用?}
        WASM_CHECK --> |是| WASM_NATIVE[原生WASM]
        WASM_CHECK --> |否| JS_FALLBACK[JS模拟器]
        WASM_NATIVE --> V2SIG[v2签名]
        JS_FALLBACK --> V2SIG
    end
    
    REQ --> US
    NONCE --> US
    TS --> US
    
    V1SIG --> RESULT[最终签名]
    V2SIG --> RESULT
```

## 📦 模块依赖关系

```mermaid
graph TD
    subgraph "外部依赖"
        AXIOS[axios - HTTP客户端]
        UUID[uuid - ID生成]
        ESS[eventsource - SSE支持]
    end
    
    subgraph "核心模块"
        INDEX[index.ts - 主入口]
        PROVIDER[providers/ - Provider层]
        SERVICES[services/ - Service层]
        TYPES[types/ - 类型定义]
        UTILS[utils/ - 工具函数]
        WASM[wasm/ - WebAssembly]
    end
    
    subgraph "构建输出"
        DIST[dist/ - 编译输出]
        DTS[*.d.ts - 类型声明]
    end
    
    INDEX --> PROVIDER
    INDEX --> SERVICES
    INDEX --> TYPES
    INDEX --> UTILS
    
    PROVIDER --> SERVICES
    SERVICES --> UTILS
    SERVICES --> TYPES
    UTILS --> WASM
    
    SERVICES --> AXIOS
    UTILS --> UUID
    SERVICES --> ESS
    
    INDEX --> DIST
    TYPES --> DTS
```

## 🧪 测试架构

```mermaid
graph TB
    subgraph "测试套件"
        UNIT[单元测试]
        INTEGRATION[集成测试]
    end
    
    subgraph "单元测试"
        UNIT --> UTILS_TEST[utils.test.ts]
        UNIT --> SIG_TEST[signature-v2.test.ts]
        UNIT --> WASM_TEST[wasm-signature-emulator.test.ts]
    end
    
    subgraph "集成测试"
        INTEGRATION --> PROVIDER_TEST[provider.test.ts]
        INTEGRATION --> V1V2_TEST[v1-v2-interface.test.ts]
    end
    
    subgraph "测试工具"
        JEST[Jest测试框架]
        MOCK[Mock服务]
        COVERAGE[覆盖率报告]
    end
    
    UNIT --> JEST
    INTEGRATION --> JEST
    INTEGRATION --> MOCK
    JEST --> COVERAGE
```

## 🔧 构建和部署架构

```mermaid
graph LR
    subgraph "源代码"
        TS[TypeScript源码]
        WASM_SRC[WebAssembly源码]
    end
    
    subgraph "构建过程"
        TSC[TypeScript编译器]
        WABT[WebAssembly工具]
        LINT[ESLint检查]
        TEST[Jest测试]
    end
    
    subgraph "输出产物"
        JS[JavaScript代码]
        DTS[类型声明文件]
        WASM_BIN[WASM二进制文件]
        MAPS[Source Maps]
    end
    
    subgraph "发布"
        NPM[NPM包]
        DOCS[文档站点]
    end
    
    TS --> TSC
    TS --> LINT
    WASM_SRC --> WABT
    
    TSC --> JS
    TSC --> DTS
    TSC --> MAPS
    WABT --> WASM_BIN
    
    TEST --> |验证| JS
    
    JS --> NPM
    DTS --> NPM
    WASM_BIN --> NPM
    DOCS --> |部署| NPM
```

## 🚀 性能优化策略

### 1. WebAssembly优化
- **内存管理**: 自动内存分配和释放
- **批量处理**: 支持批量签名计算
- **缓存机制**: 缓存WASM模块实例

### 2. HTTP客户端优化
- **连接复用**: 复用HTTP连接
- **请求合并**: 合并相似请求
- **智能重试**: 指数退避重试策略

### 3. 流式响应优化
- **缓冲管理**: 优化消息缓冲区
- **内存清理**: 及时清理无用数据
- **连接池**: 管理SSE连接池

## 📈 监控和调试

### 1. 日志系统
- **分级日志**: ERROR, WARN, INFO, DEBUG
- **结构化日志**: JSON格式输出
- **性能日志**: 请求耗时统计

### 2. 错误处理
- **错误分类**: 网络错误、API错误、参数错误
- **错误恢复**: 自动重试和降级策略
- **错误上报**: 详细的错误上下文

### 3. 性能监控
- **响应时间**: API调用耗时统计
- **成功率**: 请求成功率监控
- **资源使用**: 内存和CPU使用情况

这个架构设计确保了系统的可扩展性、可维护性和高性能，为用户提供了稳定可靠的当贝AI API调用服务。
