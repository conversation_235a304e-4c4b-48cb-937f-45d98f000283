/**
 * OpenAI兼容性测试脚本
 * 测试当贝AI Provider的OpenAI兼容接口
 */

const axios = require('axios');

// API 基础URL
const BASE_URL = 'http://localhost:3001';

/**
 * 测试OpenAI兼容的模型列表接口
 */
async function testOpenAIModels() {
  console.log('\n🔍 测试OpenAI兼容模型列表接口...');
  try {
    const response = await axios.get(`${BASE_URL}/v1/models`);
    const data = response.data;
    
    console.log('✅ OpenAI兼容模型列表请求成功');
    console.log(`📋 响应格式验证:`);
    console.log(`   - object: ${data.object} ${data.object === 'list' ? '✅' : '❌'}`);
    console.log(`   - data数组: ${Array.isArray(data.data) ? '✅' : '❌'}`);
    console.log(`   - 模型数量: ${data.data.length}`);
    
    if (data.data.length > 0) {
      const firstModel = data.data[0];
      console.log(`📝 第一个模型验证:`);
      console.log(`   - id: ${firstModel.id} ${typeof firstModel.id === 'string' ? '✅' : '❌'}`);
      console.log(`   - object: ${firstModel.object} ${firstModel.object === 'model' ? '✅' : '❌'}`);
      console.log(`   - created: ${firstModel.created} ${typeof firstModel.created === 'number' ? '✅' : '❌'}`);
      console.log(`   - owned_by: ${firstModel.owned_by} ${typeof firstModel.owned_by === 'string' ? '✅' : '❌'}`);
      console.log(`   - display_name: ${firstModel.display_name}`);
      console.log(`   - context_length: ${firstModel.context_length}`);
      
      if (firstModel.capabilities) {
        console.log(`   - 能力支持:`);
        console.log(`     * 聊天: ${firstModel.capabilities.chat ? '✅' : '❌'}`);
        console.log(`     * 补全: ${firstModel.capabilities.completion ? '✅' : '❌'}`);
        console.log(`     * 嵌入: ${firstModel.capabilities.embeddings ? '✅' : '❌'}`);
      }
      
      if (firstModel.metadata) {
        console.log(`   - 元数据:`);
        console.log(`     * 推荐: ${firstModel.metadata.recommended ? '✅' : '❌'}`);
        console.log(`     * 置顶: ${firstModel.metadata.pinned ? '✅' : '❌'}`);
        console.log(`     * 选项数量: ${firstModel.metadata.options?.length || 0}`);
      }
    }
    
    return data;
  } catch (error) {
    console.error('❌ OpenAI兼容模型列表请求失败:', error.message);
    if (error.response) {
      console.error('📋 错误响应:', error.response.data);
    }
    return null;
  }
}

/**
 * 测试OpenAI兼容的单个模型接口
 */
async function testOpenAIModelById(modelId) {
  console.log(`\n🔍 测试OpenAI兼容单个模型接口 (${modelId})...`);
  try {
    const response = await axios.get(`${BASE_URL}/v1/models/${modelId}`);
    const model = response.data;
    
    console.log('✅ OpenAI兼容单个模型请求成功');
    console.log(`📝 模型信息:`);
    console.log(`   - ID: ${model.id}`);
    console.log(`   - 名称: ${model.display_name}`);
    console.log(`   - 描述: ${model.description?.substring(0, 50)}...`);
    console.log(`   - 上下文长度: ${model.context_length}`);
    console.log(`   - 拥有者: ${model.owned_by}`);
    
    return model;
  } catch (error) {
    console.error(`❌ OpenAI兼容单个模型请求失败 (${modelId}):`, error.message);
    if (error.response) {
      console.error('📋 错误响应:', error.response.data);
    }
    return null;
  }
}

/**
 * 测试OpenAI兼容的聊天接口
 */
async function testOpenAIChat(modelId) {
  console.log(`\n🔍 测试OpenAI兼容聊天接口 (${modelId})...`);
  try {
    const requestData = {
      model: modelId,
      messages: [
        {
          role: 'user',
          content: '请用一句话介绍你自己，这是OpenAI兼容性测试'
        }
      ],
      stream: false,
      max_tokens: 100
    };
    
    console.log('📤 发送OpenAI格式聊天请求...');
    const startTime = Date.now();
    
    const response = await axios.post(`${BASE_URL}/v1/chat/completions`, requestData, {
      timeout: 30000
    });
    
    const duration = Date.now() - startTime;
    
    // 检查响应格式
    if (response.data.success !== undefined) {
      // 当前实现返回的是内部格式，需要转换
      console.log('⚠️ 注意：当前返回的是内部格式，不是OpenAI标准格式');
      const { data } = response.data;
      console.log(`✅ 聊天请求成功 (耗时: ${duration}ms)`);
      console.log(`🤖 模型: ${data.model}`);
      console.log(`💬 回复: ${data.message.content.substring(0, 100)}...`);
    } else {
      // 标准OpenAI格式
      console.log(`✅ OpenAI兼容聊天请求成功 (耗时: ${duration}ms)`);
      console.log(`🤖 模型: ${response.data.model}`);
      if (response.data.choices && response.data.choices[0]) {
        console.log(`💬 回复: ${response.data.choices[0].message.content.substring(0, 100)}...`);
      }
    }
    
    return response.data;
  } catch (error) {
    console.error(`❌ OpenAI兼容聊天请求失败 (${modelId}):`, error.message);
    if (error.response) {
      console.error('📋 错误详情:', error.response.data);
    }
    return null;
  }
}

/**
 * 对比原始接口和OpenAI兼容接口
 */
async function compareAPIs() {
  console.log('\n🔍 对比原始接口和OpenAI兼容接口...');
  
  try {
    // 获取原始格式
    const originalResponse = await axios.get(`${BASE_URL}/api/models`);
    const originalData = originalResponse.data;
    
    // 获取OpenAI格式
    const openaiResponse = await axios.get(`${BASE_URL}/v1/models`);
    const openaiData = openaiResponse.data;
    
    console.log('📊 接口对比结果:');
    console.log(`   原始接口模型数量: ${originalData.data.total}`);
    console.log(`   OpenAI接口模型数量: ${openaiData.data.length}`);
    console.log(`   数量一致性: ${originalData.data.total === openaiData.data.length ? '✅' : '❌'}`);
    
    // 检查模型ID一致性
    const originalIds = originalData.data.models.map(m => m.id).sort();
    const openaiIds = openaiData.data.map(m => m.id).sort();
    const idsMatch = JSON.stringify(originalIds) === JSON.stringify(openaiIds);
    console.log(`   模型ID一致性: ${idsMatch ? '✅' : '❌'}`);
    
    if (!idsMatch) {
      console.log(`   原始接口IDs: ${originalIds.join(', ')}`);
      console.log(`   OpenAI接口IDs: ${openaiIds.join(', ')}`);
    }
    
    return { originalData, openaiData };
  } catch (error) {
    console.error('❌ 接口对比失败:', error.message);
    return null;
  }
}

/**
 * 测试OpenAI客户端库兼容性
 */
async function testClientLibraryCompatibility() {
  console.log('\n🔍 测试OpenAI客户端库兼容性...');
  
  // 模拟OpenAI客户端的请求格式
  const openaiStyleRequests = [
    {
      name: '标准聊天请求',
      url: '/v1/chat/completions',
      method: 'POST',
      data: {
        model: 'deepseek',
        messages: [{ role: 'user', content: 'Hello' }]
      }
    },
    {
      name: '模型列表请求',
      url: '/v1/models',
      method: 'GET'
    },
    {
      name: '特定模型请求',
      url: '/v1/models/deepseek',
      method: 'GET'
    }
  ];
  
  for (const request of openaiStyleRequests) {
    try {
      console.log(`📤 测试: ${request.name}`);
      
      let response;
      if (request.method === 'GET') {
        response = await axios.get(`${BASE_URL}${request.url}`);
      } else {
        response = await axios.post(`${BASE_URL}${request.url}`, request.data);
      }
      
      console.log(`   ✅ ${request.name} - 状态码: ${response.status}`);
      
      // 检查响应头
      const contentType = response.headers['content-type'];
      if (contentType && contentType.includes('application/json')) {
        console.log(`   ✅ 正确的Content-Type: ${contentType}`);
      } else {
        console.log(`   ⚠️ Content-Type: ${contentType}`);
      }
      
    } catch (error) {
      console.log(`   ❌ ${request.name} - 失败: ${error.message}`);
    }
  }
}

/**
 * 主测试函数
 */
async function runOpenAICompatibilityTests() {
  console.log('🚀 开始OpenAI兼容性测试...');
  console.log(`🌐 API地址: ${BASE_URL}`);
  
  // 1. 测试健康检查
  try {
    await axios.get(`${BASE_URL}/health`);
    console.log('✅ 服务器健康检查通过');
  } catch (error) {
    console.log('❌ 服务器未就绪，请先启动服务器');
    process.exit(1);
  }
  
  // 2. 测试OpenAI兼容的模型列表
  const modelsData = await testOpenAIModels();
  if (!modelsData || modelsData.data.length === 0) {
    console.log('❌ 无法获取模型列表，跳过后续测试');
    return;
  }
  
  // 3. 测试单个模型接口
  const firstModelId = modelsData.data[0].id;
  await testOpenAIModelById(firstModelId);
  
  // 4. 测试聊天接口
  await testOpenAIChat(firstModelId);
  
  // 5. 对比原始接口和OpenAI接口
  await compareAPIs();
  
  // 6. 测试客户端库兼容性
  await testClientLibraryCompatibility();
  
  console.log('\n🎉 OpenAI兼容性测试完成！');
  console.log('\n📋 总结:');
  console.log('✅ 已实现OpenAI兼容的模型列表接口');
  console.log('✅ 已实现OpenAI兼容的单个模型接口');
  console.log('⚠️ 聊天接口需要进一步优化以完全兼容OpenAI格式');
  console.log('✅ 基本的客户端库兼容性支持');
}

// 运行测试
if (require.main === module) {
  runOpenAICompatibilityTests().catch(error => {
    console.error('❌ 测试运行失败:', error);
    process.exit(1);
  });
}

module.exports = {
  testOpenAIModels,
  testOpenAIModelById,
  testOpenAIChat,
  compareAPIs,
  testClientLibraryCompatibility,
  runOpenAICompatibilityTests
};
