#!/usr/bin/env node

/**
 * 文本生成接口测试脚本
 * 测试HTTP API的文本生成功能
 */

const http = require('http');
const https = require('https');

// 配置
const config = {
  host: 'localhost',
  port: 3000,
  timeout: 30000
};

/**
 * 发送HTTP请求
 * @param {Object} options 请求选项
 * @param {Object} data 请求数据
 * @returns {Promise} 响应数据
 */
function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const client = options.protocol === 'https:' ? https : http;
    
    const req = client.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsed = responseData ? JSON.parse(responseData) : {};
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: parsed
          });
        } catch (error) {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: responseData
          });
        }
      });
    });
    
    req.on('error', reject);
    req.setTimeout(config.timeout, () => {
      req.destroy();
      reject(new Error('请求超时'));
    });
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

/**
 * 发送流式请求
 * @param {Object} options 请求选项
 * @param {Object} data 请求数据
 * @returns {Promise} 流式响应处理
 */
function makeStreamRequest(options, data) {
  return new Promise((resolve, reject) => {
    const client = options.protocol === 'https:' ? https : http;
    
    const req = client.request(options, (res) => {
      console.log(`\n📡 流式响应开始 (状态码: ${res.statusCode})`);
      console.log('📝 生成内容:');
      
      let chunks = [];
      let fullText = '';
      
      res.on('data', (chunk) => {
        const lines = chunk.toString().split('\n');
        
        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            
            if (data === '[DONE]') {
              console.log('\n✅ 流式生成完成');
              resolve({
                statusCode: res.statusCode,
                chunks,
                fullText
              });
              return;
            }
            
            try {
              const parsed = JSON.parse(data);
              chunks.push(parsed);
              
              if (parsed.choices && parsed.choices[0] && parsed.choices[0].delta && parsed.choices[0].delta.text) {
                const text = parsed.choices[0].delta.text;
                fullText += text;
                process.stdout.write(text);
              }
            } catch (error) {
              // 忽略解析错误，继续处理
            }
          }
        }
      });
      
      res.on('end', () => {
        console.log('\n✅ 流式响应结束');
        resolve({
          statusCode: res.statusCode,
          chunks,
          fullText
        });
      });
    });
    
    req.on('error', reject);
    req.setTimeout(config.timeout, () => {
      req.destroy();
      reject(new Error('流式请求超时'));
    });
    
    req.write(JSON.stringify(data));
    req.end();
  });
}

/**
 * 测试获取文本生成模型列表
 */
async function testGetTextModels() {
  console.log('\n🧪 测试: 获取文本生成模型列表');
  console.log('📡 GET /api/text/models');
  
  try {
    const response = await makeRequest({
      hostname: config.host,
      port: config.port,
      path: '/api/text/models',
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    console.log(`📊 状态码: ${response.statusCode}`);
    
    if (response.statusCode === 200) {
      console.log('✅ 请求成功');
      console.log(`📋 模型总数: ${response.data.data?.total || 0}`);
      console.log(`🎯 默认模型: ${response.data.data?.defaultModel || 'N/A'}`);
      
      if (response.data.data?.recommendations) {
        console.log('💡 任务推荐:');
        Object.entries(response.data.data.recommendations).forEach(([task, models]) => {
          console.log(`   ${task}: ${models.join(', ')}`);
        });
      }
    } else {
      console.log('❌ 请求失败');
      console.log('📄 响应:', JSON.stringify(response.data, null, 2));
    }
  } catch (error) {
    console.log('❌ 请求异常:', error.message);
  }
}

/**
 * 测试文本生成（非流式）
 */
async function testTextGeneration() {
  console.log('\n🧪 测试: 文本生成（非流式）');
  console.log('📡 POST /api/text/generate');
  
  const requestData = {
    prompt: '请写一首关于人工智能的现代诗，要求有创意和想象力',
    model: 'deepseek',
    stream: false,
    task_type: 'creative',
    max_tokens: 500,
    temperature: 0.8,
    options: {
      style: '现代诗',
      format: 'markdown',
      language: 'zh',
      deep_thinking: true
    }
  };
  
  console.log('📝 请求参数:', JSON.stringify(requestData, null, 2));
  
  try {
    const response = await makeRequest({
      hostname: config.host,
      port: config.port,
      path: '/api/text/generate',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    }, requestData);
    
    console.log(`📊 状态码: ${response.statusCode}`);
    
    if (response.statusCode === 200) {
      console.log('✅ 请求成功');
      console.log(`🤖 使用模型: ${response.data.data?.model || 'N/A'}`);
      console.log(`📋 任务类型: ${response.data.data?.task_type || 'N/A'}`);
      console.log(`🔚 完成原因: ${response.data.data?.finish_reason || 'N/A'}`);
      console.log(`⏱️ 生成时间: ${response.data.data?.metadata?.generation_time || 'N/A'}ms`);
      
      if (response.data.data?.usage) {
        console.log('📊 使用统计:');
        console.log(`   提示令牌: ${response.data.data.usage.prompt_tokens || 0}`);
        console.log(`   完成令牌: ${response.data.data.usage.completion_tokens || 0}`);
        console.log(`   总令牌: ${response.data.data.usage.total_tokens || 0}`);
      }
      
      console.log('\n📝 生成内容:');
      console.log('─'.repeat(50));
      console.log(response.data.data?.text || '无内容');
      console.log('─'.repeat(50));
    } else {
      console.log('❌ 请求失败');
      console.log('📄 响应:', JSON.stringify(response.data, null, 2));
    }
  } catch (error) {
    console.log('❌ 请求异常:', error.message);
  }
}

/**
 * 测试文本生成（流式）
 */
async function testStreamTextGeneration() {
  console.log('\n🧪 测试: 文本生成（流式）');
  console.log('📡 POST /api/text/generate (stream)');
  
  const requestData = {
    prompt: '请详细解释什么是深度学习，包括其原理、应用和发展前景',
    model: 'deepseek',
    stream: true,
    task_type: 'qa',
    max_tokens: 800,
    temperature: 0.3,
    options: {
      format: 'markdown',
      deep_thinking: true
    }
  };
  
  console.log('📝 请求参数:', JSON.stringify(requestData, null, 2));
  
  try {
    const response = await makeStreamRequest({
      hostname: config.host,
      port: config.port,
      path: '/api/text/generate',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'text/event-stream'
      }
    }, requestData);
    
    console.log(`\n📊 最终状态码: ${response.statusCode}`);
    console.log(`📦 接收数据块: ${response.chunks.length}`);
    console.log(`📏 生成文本长度: ${response.fullText.length} 字符`);
    
  } catch (error) {
    console.log('❌ 流式请求异常:', error.message);
  }
}

/**
 * 测试代码生成
 */
async function testCodeGeneration() {
  console.log('\n🧪 测试: 代码生成');
  console.log('📡 POST /api/text/generate (code)');
  
  const requestData = {
    prompt: '请用Python实现一个简单的二分查找算法，包含详细注释',
    model: 'deepseek',
    stream: false,
    task_type: 'code',
    max_tokens: 600,
    temperature: 0.1,
    options: {
      format: 'markdown',
      language: 'zh'
    }
  };
  
  try {
    const response = await makeRequest({
      hostname: config.host,
      port: config.port,
      path: '/api/text/generate',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    }, requestData);
    
    console.log(`📊 状态码: ${response.statusCode}`);
    
    if (response.statusCode === 200) {
      console.log('✅ 代码生成成功');
      console.log('\n💻 生成的代码:');
      console.log('─'.repeat(50));
      console.log(response.data.data?.text || '无内容');
      console.log('─'.repeat(50));
    } else {
      console.log('❌ 代码生成失败');
      console.log('📄 响应:', JSON.stringify(response.data, null, 2));
    }
  } catch (error) {
    console.log('❌ 代码生成异常:', error.message);
  }
}

/**
 * 测试参数验证
 */
async function testParameterValidation() {
  console.log('\n🧪 测试: 参数验证');
  console.log('📡 POST /api/text/generate (invalid params)');

  // 测试空提示词
  const invalidRequest = {
    prompt: '',
    model: 'deepseek'
  };

  try {
    const response = await makeRequest({
      hostname: config.host,
      port: config.port,
      path: '/api/text/generate',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    }, invalidRequest);

    console.log(`📊 状态码: ${response.statusCode}`);

    if (response.statusCode === 400) {
      console.log('✅ 参数验证正常工作');
      console.log('📄 错误信息:', response.data.error?.message || 'N/A');
    } else {
      console.log('❌ 参数验证可能有问题');
      console.log('📄 响应:', JSON.stringify(response.data, null, 2));
    }
  } catch (error) {
    console.log('❌ 参数验证测试异常:', error.message);
  }
}

/**
 * 主测试函数
 */
async function runTests() {
  console.log('🚀 开始文本生成接口测试');
  console.log(`🌐 服务器: http://${config.host}:${config.port}`);
  console.log(`⏱️ 超时时间: ${config.timeout}ms`);
  console.log('=' .repeat(60));

  const startTime = Date.now();

  try {
    // 1. 测试获取模型列表
    await testGetTextModels();

    // 2. 测试文本生成（非流式）
    await testTextGeneration();

    // 3. 测试文本生成（流式）
    await testStreamTextGeneration();

    // 4. 测试代码生成
    await testCodeGeneration();

    // 5. 测试参数验证
    await testParameterValidation();

  } catch (error) {
    console.log('\n❌ 测试过程中发生异常:', error.message);
  }

  const endTime = Date.now();
  const duration = endTime - startTime;

  console.log('\n' + '='.repeat(60));
  console.log(`🏁 测试完成，总耗时: ${duration}ms`);
  console.log('📋 测试项目:');
  console.log('   ✓ 获取文本生成模型列表');
  console.log('   ✓ 文本生成（非流式）');
  console.log('   ✓ 文本生成（流式）');
  console.log('   ✓ 代码生成');
  console.log('   ✓ 参数验证');
  console.log('\n💡 提示: 确保服务器已启动并运行在指定端口');
}

/**
 * 处理命令行参数
 */
function parseArgs() {
  const args = process.argv.slice(2);

  for (let i = 0; i < args.length; i++) {
    switch (args[i]) {
      case '--host':
        config.host = args[++i];
        break;
      case '--port':
        config.port = parseInt(args[++i]);
        break;
      case '--timeout':
        config.timeout = parseInt(args[++i]);
        break;
      case '--help':
        console.log('文本生成接口测试脚本');
        console.log('');
        console.log('用法: node test-text-generation.js [选项]');
        console.log('');
        console.log('选项:');
        console.log('  --host <host>       服务器主机 (默认: localhost)');
        console.log('  --port <port>       服务器端口 (默认: 3000)');
        console.log('  --timeout <ms>      请求超时时间 (默认: 30000)');
        console.log('  --help              显示帮助信息');
        console.log('');
        console.log('示例:');
        console.log('  node test-text-generation.js');
        console.log('  node test-text-generation.js --host 127.0.0.1 --port 8080');
        process.exit(0);
        break;
    }
  }
}

// 主程序入口
if (require.main === module) {
  parseArgs();
  runTests().catch(console.error);
}
