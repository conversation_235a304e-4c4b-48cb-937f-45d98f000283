#!/usr/bin/env node

/**
 * 当贝AI模型测试脚本
 * 快速测试不同AI模型的调用功能
 * 
 * 使用方法：
 * node test-models.js
 */

const { DangbeiProvider } = require('./dist');

// 从models.json提取的主要模型
const MODELS = {
  // 推荐的推理模型
  DEEPSEEK_R1: 'deepseek',                    // DeepSeek-R1最新版 - 逻辑推理专家
  DOUBAO_1_6: 'doubao-1_6-thinking',         // 豆包-1.6 - 创作推理增强
  GLM_4_5: 'glm-4-5',                        // GLM-4.5 - 智谱旗舰模型
  
  // 高效响应模型
  DEEPSEEK_V3: 'deepseek-v3',                // DeepSeek-V3 - 代码专家
  KIMI_K2: 'kimi-k2-0711-preview',          // Kimi K2 - Agent任务专家
  QWEN_PLUS: 'qwen-plus',                    // 通义Plus - 问题解析专家
  DOUBAO: 'doubao',                          // 豆包 - 全能AI
};

/**
 * 测试单个模型
 */
async function testSingleModel(provider, conversationId, modelName, modelValue, question) {
  console.log(`\n🤖 测试模型: ${modelName} (${modelValue})`);
  console.log('-'.repeat(60));
  
  try {
    const startTime = Date.now();
    
    const response = await provider.chatSync({
      conversationId: conversationId,
      question: question,
      model: modelValue
    });
    
    const duration = Date.now() - startTime;
    
    console.log(`✅ 调用成功 (${duration}ms)`);
    console.log(`回复长度: ${response.length} 字符`);
    console.log(`回复预览: ${response.substring(0, 150)}...`);
    
    return { success: true, duration, responseLength: response.length };
    
  } catch (error) {
    console.log(`❌ 调用失败: ${error.message}`);
    return { success: false, error: error.message };
  }
}

/**
 * 快速测试 - 测试所有主要模型
 */
async function quickTest() {
  console.log('=== 当贝AI模型快速测试 ===\n');
  
  const provider = new DangbeiProvider({
    debug: false,  // 关闭调试日志以便查看测试结果
    timeout: 45000
  });

  try {
    // 创建测试对话
    console.log('📝 创建测试对话...');
    const conversation = await provider.createConversation({
      source: 'model-test'
    });
    console.log(`对话ID: ${conversation.conversationId}`);

    // 测试问题
    const testQuestion = '请用一句话介绍什么是人工智能';
    console.log(`测试问题: ${testQuestion}`);

    // 测试结果统计
    const results = [];

    // 测试主要模型
    const modelsToTest = [
      { name: 'DeepSeek-R1（推理专家）', value: MODELS.DEEPSEEK_R1 },
      { name: '豆包-1.6（创作增强）', value: MODELS.DOUBAO_1_6 },
      { name: 'DeepSeek-V3（代码专家）', value: MODELS.DEEPSEEK_V3 },
      { name: '通义Plus（问题解析）', value: MODELS.QWEN_PLUS }
    ];

    for (const model of modelsToTest) {
      const result = await testSingleModel(
        provider, 
        conversation.conversationId, 
        model.name, 
        model.value, 
        testQuestion
      );
      
      results.push({
        name: model.name,
        value: model.value,
        ...result
      });

      // 避免请求过于频繁
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    // 显示测试总结
    console.log('\n' + '='.repeat(80));
    console.log('📊 测试总结');
    console.log('='.repeat(80));

    const successCount = results.filter(r => r.success).length;
    const totalCount = results.length;

    console.log(`总测试数: ${totalCount}`);
    console.log(`成功数: ${successCount}`);
    console.log(`成功率: ${(successCount / totalCount * 100).toFixed(1)}%`);

    if (successCount > 0) {
      const avgDuration = results
        .filter(r => r.success)
        .reduce((sum, r) => sum + r.duration, 0) / successCount;
      
      console.log(`平均响应时间: ${avgDuration.toFixed(0)}ms`);
    }

    console.log('\n详细结果:');
    results.forEach(result => {
      const status = result.success ? '✅' : '❌';
      const info = result.success 
        ? `${result.duration}ms, ${result.responseLength}字符`
        : result.error;
      
      console.log(`${status} ${result.name}: ${info}`);
    });

  } catch (error) {
    console.error('测试失败:', error.message);
  }
}

/**
 * 深度测试 - 测试不同类型的问题
 */
async function deepTest() {
  console.log('\n=== 深度功能测试 ===\n');
  
  const provider = new DangbeiProvider({
    debug: false,
    timeout: 60000
  });

  try {
    const conversation = await provider.createConversation();

    // 测试场景
    const testScenarios = [
      {
        name: '逻辑推理测试',
        question: '如果所有的鸟都会飞，企鹅是鸟，那么企鹅会飞吗？请分析这个逻辑问题。',
        model: MODELS.DEEPSEEK_R1,
        modelName: 'DeepSeek-R1'
      },
      {
        name: '代码分析测试',
        question: '请解释这段代码的功能：\n```javascript\nfunction quickSort(arr) {\n  if (arr.length <= 1) return arr;\n  const pivot = arr[0];\n  const left = arr.slice(1).filter(x => x < pivot);\n  const right = arr.slice(1).filter(x => x >= pivot);\n  return [...quickSort(left), pivot, ...quickSort(right)];\n}\n```',
        model: MODELS.DEEPSEEK_V3,
        modelName: 'DeepSeek-V3'
      },
      {
        name: '创意写作测试',
        question: '请写一首关于春天的七言绝句',
        model: MODELS.GLM_4_5,
        modelName: 'GLM-4.5'
      },
      {
        name: '知识问答测试',
        question: '请简要介绍量子计算的基本原理和应用前景',
        model: MODELS.QWEN_PLUS,
        modelName: '通义Plus'
      }
    ];

    for (const scenario of testScenarios) {
      console.log(`\n🎯 ${scenario.name}`);
      console.log(`模型: ${scenario.modelName} (${scenario.model})`);
      console.log(`问题: ${scenario.question.substring(0, 100)}...`);
      console.log('-'.repeat(60));

      try {
        const startTime = Date.now();
        
        const response = await provider.chatSync({
          conversationId: conversation.conversationId,
          question: scenario.question,
          model: scenario.model
        });
        
        const duration = Date.now() - startTime;
        
        console.log(`✅ 测试成功 (${duration}ms)`);
        console.log(`回复: ${response.substring(0, 200)}...`);
        
      } catch (error) {
        console.log(`❌ 测试失败: ${error.message}`);
      }

      // 等待避免请求过频
      await new Promise(resolve => setTimeout(resolve, 2000));
    }

  } catch (error) {
    console.error('深度测试失败:', error.message);
  }
}

/**
 * 流式响应测试
 */
async function streamTest() {
  console.log('\n=== 流式响应测试 ===\n');
  
  const provider = new DangbeiProvider({
    debug: false
  });

  try {
    console.log('🌊 测试流式响应功能...');
    console.log('问题: 请讲一个关于AI的有趣故事');
    console.log('模型: GLM-4.5');
    console.log('-'.repeat(60));
    console.log('AI回复: ');

    let totalContent = '';
    const startTime = Date.now();

    await provider.quickChat('请讲一个关于AI的有趣故事，要求简短有趣', {
      onMessage: (content) => {
        process.stdout.write(content);
        totalContent += content;
      },
      onComplete: () => {
        const duration = Date.now() - startTime;
        console.log(`\n\n✅ 流式响应完成`);
        console.log(`总时长: ${duration}ms`);
        console.log(`总字符数: ${totalContent.length}`);
      },
      onError: (error) => {
        console.error(`\n❌ 流式响应失败: ${error.message}`);
      }
    });

  } catch (error) {
    console.error('流式测试失败:', error.message);
  }
}

/**
 * 主函数
 */
async function main() {
  const args = process.argv.slice(2);
  const testType = args[0] || 'quick';

  console.log('🚀 当贝AI模型测试工具');
  console.log(`测试类型: ${testType}`);
  console.log('时间:', new Date().toLocaleString());
  console.log('='.repeat(80));

  try {
    switch (testType) {
      case 'quick':
        await quickTest();
        break;
      case 'deep':
        await deepTest();
        break;
      case 'stream':
        await streamTest();
        break;
      case 'all':
        await quickTest();
        await deepTest();
        await streamTest();
        break;
      default:
        console.log('使用方法:');
        console.log('  node test-models.js quick   # 快速测试主要模型');
        console.log('  node test-models.js deep    # 深度功能测试');
        console.log('  node test-models.js stream  # 流式响应测试');
        console.log('  node test-models.js all     # 运行所有测试');
        return;
    }

    console.log('\n🎉 测试完成！');

  } catch (error) {
    console.error('测试执行失败:', error);
    process.exit(1);
  }
}

// 运行主函数
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  MODELS,
  quickTest,
  deepTest,
  streamTest
};
