# 当贝AI模型调用使用指南

## 快速开始

### 1. 安装依赖

```bash
npm install
npm run build
```

### 2. 快速测试所有模型

```bash
# 快速测试主要模型
npm run test-models

# 或者直接运行
node test-models.js quick
```

### 3. 运行示例代码

```bash
# JavaScript简单示例
npm run example-basic

# TypeScript完整示例
npm run example-advanced
```

## 支持的AI模型

基于 `models.json` 文件，当贝AI支持以下模型：

### 🧠 推理模型（深度思考）
- **deepseek** - DeepSeek-R1最新版 ⭐⭐⭐⭐⭐
- **doubao-1_6-thinking** - 豆包-1.6 ⭐⭐⭐⭐⭐
- **glm-4-5** - GLM-4.5 ⭐⭐⭐⭐
- **qwen3-235b-a22b** - 通义3-235B ⭐⭐⭐⭐
- **MiniMax-M1** - MiniMax-M1 ⭐⭐⭐
- **qwq-plus** - 通义QwQ ⭐⭐⭐
- **doubao-thinking** - 豆包-1.5 ⭐⭐⭐

### ⚡ 高效模型（快速响应）
- **deepseek-v3** - DeepSeek-V3 ⭐⭐⭐⭐
- **kimi-k2-0711-preview** - Kimi K2 ⭐⭐⭐⭐
- **glm-4-plus** - GLM-4-Plus ⭐⭐⭐
- **doubao** - 豆包 ⭐⭐⭐
- **qwen-plus** - 通义Plus ⭐⭐⭐
- **moonshot-v1-32k** - Kimi ⭐⭐⭐
- **qwen-long** - 通义Long ⭐⭐
- **ernie-4.5-turbo-32k** - 文心4.5 ⭐⭐

## 基础调用示例

### 1. 快速聊天（默认模型）

```javascript
const { DangbeiProvider } = require('./dist');

const provider = new DangbeiProvider({ debug: true });

async function quickChat() {
  const response = await provider.quickChat('你好，请介绍一下你自己');
  console.log('AI回复:', response.content);
}

quickChat();
```

### 2. 指定模型调用

```javascript
async function useSpecificModel() {
  const conversation = await provider.createConversation();
  
  // 使用DeepSeek-R1进行逻辑推理
  const response = await provider.chatSync({
    conversationId: conversation.conversationId,
    question: '请分析：为什么1+1=2？',
    model: 'deepseek'  // 指定模型
  });
  
  console.log('DeepSeek-R1回复:', response);
}
```

### 3. 流式响应

```javascript
async function streamingChat() {
  await provider.quickChat('请写一首关于AI的诗', {
    onMessage: (content) => {
      process.stdout.write(content);  // 实时显示
    },
    onComplete: () => {
      console.log('\n✅ 完成！');
    },
    onError: (error) => {
      console.error('❌ 错误:', error.message);
    }
  });
}
```

## 测试命令

### 模型测试

```bash
# 快速测试主要模型
npm run test-models

# 深度功能测试
npm run test-models-deep

# 流式响应测试
npm run test-models-stream

# 运行所有测试
npm run test-models-all
```

### 示例运行

```bash
# JavaScript简单示例
npm run example-basic

# TypeScript完整示例
npm run example-advanced
```

### 其他测试

```bash
# 单元测试
npm test

# WASM签名测试
npm run test-wasm

# 性能基准测试
npm run benchmark
```

## 模型选择建议

### 按场景选择

| 使用场景 | 推荐模型 | 模型值 |
|----------|----------|--------|
| 逻辑推理 | DeepSeek-R1 | `deepseek` |
| 代码分析 | DeepSeek-V3 | `deepseek-v3` |
| 创意写作 | 豆包-1.6 | `doubao-1_6-thinking` |
| 快速问答 | 通义Plus | `qwen-plus` |
| 数学编程 | 豆包-1.5 | `doubao-thinking` |
| 长文本处理 | 通义Long | `qwen-long` |

### 按响应时间选择

- **快速响应（3-10秒）**: deepseek-v3, kimi-k2-0711-preview, qwen-plus
- **中等响应（10-20秒）**: glm-4-plus, moonshot-v1-32k, doubao
- **深度思考（20-60秒）**: deepseek, doubao-1_6-thinking, glm-4-5

## 高级配置

### 启用知识搜索

```javascript
const response = await provider.chatSync({
  conversationId: conversation.conversationId,
  question: '请分析2024年AI技术发展趋势',
  model: 'glm-4-5',
  chatOption: {
    searchKnowledge: true,        // 启用知识搜索
    searchAllKnowledge: false,    // 不搜索所有知识库
    searchSharedKnowledge: true   // 启用共享知识库
  }
});
```

### 错误处理

```javascript
async function robustCall() {
  try {
    const response = await provider.chatSync({
      conversationId: 'your-conversation-id',
      question: 'your-question',
      model: 'deepseek'
    });
    return response;
  } catch (error) {
    console.error('主模型失败:', error.message);
    
    // 使用备用模型
    try {
      const fallbackResponse = await provider.chatSync({
        conversationId: 'your-conversation-id',
        question: 'your-question',
        model: 'doubao'  // 备用模型
      });
      return fallbackResponse;
    } catch (fallbackError) {
      console.error('备用模型也失败:', fallbackError.message);
      throw fallbackError;
    }
  }
}
```

## 文件说明

### 核心文件
- `test-models.js` - 模型测试脚本
- `examples/model-usage-simple.js` - JavaScript简单示例
- `examples/model-usage-examples.ts` - TypeScript完整示例
- `models.json` - 支持的模型列表数据

### 文档文件
- `docs/模型调用指南.md` - 详细使用指南
- `docs/支持的模型列表.md` - 完整模型列表
- `docs/api.md` - API参考文档

### 配置文件
- `package.json` - 项目配置和脚本
- `tsconfig.json` - TypeScript配置
- `jest.config.js` - 测试配置

## 注意事项

1. **推理模型**响应时间较长，建议设置60秒超时
2. **高效模型**响应快速，适合实时交互
3. 某些模型的深度思考功能可能被禁用
4. 建议根据具体需求选择合适的模型
5. 避免过于频繁的请求，建议间隔2-3秒

## 故障排除

### 常见问题

1. **模型调用失败**
   - 检查网络连接
   - 尝试使用备用模型
   - 查看调试日志

2. **响应时间过长**
   - 推理模型需要更长时间
   - 增加超时设置
   - 考虑使用高效模型

3. **签名验证失败**
   - 检查WASM模块是否正常
   - 尝试重新编译WASM
   - 使用备用签名算法

### 调试模式

```javascript
const provider = new DangbeiProvider({
  debug: true,      // 启用调试日志
  timeout: 60000    // 设置超时时间
});
```

## 更多资源

- [完整API文档](./docs/api.md)
- [开发指南](./docs/development.md)
- [WebAssembly使用指南](./WASM_README.md)
- [项目实现总结](./项目实现总结.md)
