#!/usr/bin/env node

/**
 * 三色数据排查和测试脚本
 * 用于系统性地测试和验证当贝提供商系统中三色数据的处理情况
 * 
 * 三色数据类型：
 * - 蓝色 (progress): 联网搜索进度信息
 * - 黄色 (thinking): AI思考过程
 * - 绿色 (text): 正式回答内容
 * - 主色调 (card): 搜索结果卡片
 */

const { DangbeiProvider } = require('./dist');

// 颜色输出函数
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m'
};

function colorLog(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

/**
 * 三色数据统计器
 */
class ThreeColorDataStats {
  constructor() {
    this.stats = {
      progress: { count: 0, totalLength: 0, messages: [] },
      thinking: { count: 0, totalLength: 0, messages: [] },
      text: { count: 0, totalLength: 0, messages: [] },
      card: { count: 0, totalLength: 0, messages: [] },
      unknown: { count: 0, totalLength: 0, messages: [] }
    };
    this.startTime = Date.now();
    this.totalMessages = 0;
  }

  recordMessage(content, data) {
    this.totalMessages++;
    const contentType = data.content_type || 'unknown';
    
    if (!this.stats[contentType]) {
      this.stats[contentType] = { count: 0, totalLength: 0, messages: [] };
    }
    
    this.stats[contentType].count++;
    this.stats[contentType].totalLength += content.length;
    this.stats[contentType].messages.push({
      timestamp: Date.now(),
      length: content.length,
      preview: content.substring(0, 50) + (content.length > 50 ? '...' : ''),
      messageId: data.id
    });

    // 实时显示接收到的消息
    this.displayMessage(contentType, content, data);
  }

  displayMessage(contentType, content, data) {
    const icons = {
      progress: '🔍',
      thinking: '🤔', 
      text: '💬',
      card: '📋',
      unknown: '❓'
    };

    const colorMap = {
      progress: 'blue',
      thinking: 'yellow',
      text: 'green', 
      card: 'magenta',
      unknown: 'red'
    };

    const icon = icons[contentType] || '❓';
    const color = colorMap[contentType] || 'red';
    const preview = content.substring(0, 100) + (content.length > 100 ? '...' : '');
    
    colorLog(color, `${icon} [${contentType.toUpperCase()}] ${preview}`);
    console.log(`   消息ID: ${data.id}, 长度: ${content.length} 字符, 时间: ${new Date().toLocaleTimeString()}`);
  }

  generateReport() {
    const duration = Date.now() - this.startTime;
    
    colorLog('cyan', '\n=== 三色数据统计报告 ===');
    colorLog('white', `测试持续时间: ${(duration / 1000).toFixed(2)} 秒`);
    colorLog('white', `消息总数: ${this.totalMessages}`);
    
    // 各类型统计
    Object.keys(this.stats).forEach(type => {
      const stat = this.stats[type];
      if (stat.count > 0) {
        const avgLength = Math.round(stat.totalLength / stat.count);
        const percentage = ((stat.count / this.totalMessages) * 100).toFixed(1);
        
        const colorMap = {
          progress: 'blue',
          thinking: 'yellow', 
          text: 'green',
          card: 'magenta',
          unknown: 'red'
        };
        
        const color = colorMap[type] || 'white';
        colorLog(color, `\n${type.toUpperCase()} 数据:`);
        console.log(`  数量: ${stat.count} (${percentage}%)`);
        console.log(`  总长度: ${stat.totalLength} 字符`);
        console.log(`  平均长度: ${avgLength} 字符`);
        console.log(`  最新消息: ${stat.messages[stat.messages.length - 1]?.preview || '无'}`);
      }
    });

    // 问题诊断
    this.diagnoseIssues();
  }

  diagnoseIssues() {
    colorLog('cyan', '\n=== 问题诊断 ===');
    
    const issues = [];
    const expectedTypes = ['progress', 'thinking', 'text', 'card'];
    
    // 检查缺失的数据类型
    expectedTypes.forEach(type => {
      if (this.stats[type].count === 0) {
        issues.push({
          level: 'warning',
          type: type,
          message: `未接收到${type}类型的数据`,
          suggestion: `检查数据源是否正常生成${type}类型的消息`
        });
      }
    });

    // 检查未知类型
    if (this.stats.unknown.count > 0) {
      issues.push({
        level: 'error',
        type: 'unknown',
        message: `接收到${this.stats.unknown.count}个未知类型的消息`,
        suggestion: '检查是否有新的content_type需要处理'
      });
    }

    // 检查数据比例异常
    if (this.totalMessages > 0) {
      const textRatio = this.stats.text.count / this.totalMessages;
      if (textRatio < 0.1) {
        issues.push({
          level: 'warning',
          type: 'text',
          message: 'text类型消息比例过低',
          suggestion: '检查正式回答内容是否正常生成'
        });
      }
    }

    // 输出诊断结果
    if (issues.length === 0) {
      colorLog('green', '✅ 未发现明显问题');
    } else {
      issues.forEach(issue => {
        const color = issue.level === 'error' ? 'red' : 'yellow';
        const icon = issue.level === 'error' ? '❌' : '⚠️';
        colorLog(color, `${icon} ${issue.message}`);
        console.log(`   建议: ${issue.suggestion}`);
      });
    }
  }
}

/**
 * 测试三色数据处理
 */
async function testThreeColorData() {
  colorLog('cyan', '🔍 开始三色数据排查测试');
  colorLog('cyan', '=' .repeat(50));

  const provider = new DangbeiProvider({
    debug: true
  });

  const stats = new ThreeColorDataStats();

  try {
    // 创建对话
    colorLog('blue', '\n📝 创建测试对话...');
    const conversation = await provider.createConversation();
    console.log(`对话ID: ${conversation.conversationId}`);

    // 测试问题 - 选择一个会触发多种响应类型的问题
    const testQuestion = '请帮我搜索一下最新的人工智能发展趋势，并分析一下未来的发展方向';
    
    colorLog('blue', `\n💬 发送测试问题: ${testQuestion}`);
    
    // 开始聊天并监控三色数据
    await provider.chat({
      conversationId: conversation.conversationId,
      question: testQuestion,
      callbacks: {
        onMessage: (content, data) => {
          stats.recordMessage(content, data);
        },
        onComplete: (data) => {
          colorLog('green', '\n✅ 对话完成');
          console.log(`完成消息ID: ${data.id}`);
        },
        onError: (error) => {
          colorLog('red', `\n❌ 对话出错: ${error.message}`);
        }
      }
    });

    // 等待一段时间确保所有消息都被处理
    await new Promise(resolve => setTimeout(resolve, 2000));

    // 生成统计报告
    stats.generateReport();

  } catch (error) {
    colorLog('red', `\n❌ 测试失败: ${error.message}`);
    console.error(error);
  }
}

/**
 * 测试特定类型的数据处理
 */
async function testSpecificDataType(dataType) {
  colorLog('cyan', `\n🔍 测试 ${dataType} 数据类型处理`);
  
  const questions = {
    progress: '请搜索最新的科技新闻',
    thinking: '请分析一下人工智能的发展趋势',
    text: '你好，请介绍一下自己',
    card: '请搜索并整理关于机器学习的资料'
  };

  const question = questions[dataType] || '请帮我搜索相关信息';
  
  const provider = new DangbeiProvider({ debug: true });
  const stats = new ThreeColorDataStats();

  try {
    const conversation = await provider.createConversation();
    
    await provider.chat({
      conversationId: conversation.conversationId,
      question: question,
      callbacks: {
        onMessage: (content, data) => {
          if (data.content_type === dataType) {
            stats.recordMessage(content, data);
            colorLog('green', `✅ 成功接收到 ${dataType} 类型数据`);
          }
        },
        onComplete: () => {
          colorLog('blue', `${dataType} 类型测试完成`);
        },
        onError: (error) => {
          colorLog('red', `${dataType} 类型测试失败: ${error.message}`);
        }
      }
    });

    await new Promise(resolve => setTimeout(resolve, 1000));
    
  } catch (error) {
    colorLog('red', `测试 ${dataType} 类型时出错: ${error.message}`);
  }
}

/**
 * 主函数
 */
async function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--help') || args.includes('-h')) {
    console.log(`
三色数据排查测试工具

用法:
  node test-three-color-data.js [选项]

选项:
  --type <类型>     测试特定的数据类型 (progress|thinking|text|card)
  --help, -h        显示帮助信息
  --verbose, -v     详细输出模式

示例:
  node test-three-color-data.js                    # 完整测试
  node test-three-color-data.js --type progress    # 测试progress类型
  node test-three-color-data.js --verbose          # 详细模式
`);
    return;
  }

  const typeIndex = args.indexOf('--type');
  if (typeIndex !== -1 && args[typeIndex + 1]) {
    const dataType = args[typeIndex + 1];
    await testSpecificDataType(dataType);
  } else {
    await testThreeColorData();
  }
}

// 运行测试
if (require.main === module) {
  main().catch(error => {
    colorLog('red', `\n💥 程序异常退出: ${error.message}`);
    console.error(error);
    process.exit(1);
  });
}

module.exports = {
  testThreeColorData,
  testSpecificDataType,
  ThreeColorDataStats
};
